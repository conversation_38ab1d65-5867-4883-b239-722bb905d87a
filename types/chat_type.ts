export interface ChatItem {
  id: string; // Changed to string to match Firebase user IDs
  name: string;
  avatar: string;
  lastMessage?: string;
  timestamp: string;
  unread?: number;
  memberCount?: number;
  attendeeCount?: number;
  date?: string;
  chatId?: string; // Firebase chat ID
}

export interface Message {
  _id: string; // Changed to string for Firebase compatibility
  text: string;
  createdAt: Date;
  user: {
    _id: string; // Changed to string to match Firebase user IDs
    name: string;
    avatar: string;
  };
  image?: string;
  video?: string;
}

export interface Friend {
  id: string; // Changed to string to match Firebase user IDs
  name: string;
  avatar: string;
  status: 'online' | 'offline';
  email?: string;
  fullName?: string;
}

// Firebase-specific interfaces
export interface FirebaseChatUser {
  id: string;
  name: string;
  avatar: string;
  email: string;
  fullName: string;
}
