import React, { useState, useCallback, useEffect, useRef, useMemo } from 'react';
import {
  View,
  Text,
  Platform,
  SafeAreaView,
  Keyboard,
  TouchableOpacity,
  Modal,
  TextInput,
  Image,
  Alert,
  Dimensions,
  KeyboardAvoidingView,
  ActivityIndicator,
} from 'react-native';
import { StatusBar } from 'expo-status-bar';
import ImagePicker from 'react-native-image-crop-picker';
import { useRouter } from 'expo-router';
import { COLORS } from '../../../theme/colors';
import { Ionicons } from '@expo/vector-icons';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useColorScheme } from '../../../lib/useColorScheme';
import ChatList from '../../../components/Chat/ChatList';
import ActiveChat from '../../../components/Chat/ActiveChat';
import NewChatSheet from '../../../components/Chat/NewChatSheet';
import CreateCommunityModal from '../../../components/Chat/CreateCommunityModal';
import BrowseCommunitiesModal from '../../../components/Chat/BrowseCommunitiesModal';
import BottomSheet, { BottomSheetBackdrop } from '@gorhom/bottom-sheet';
import { ChatItem, Friend, Message } from '../../../types/chat_type';
import { GiftedChat, Actions } from 'react-native-gifted-chat';
import { useFirebaseChat } from '../../../hooks/useFirebaseChat';
import { UserStore } from '../../../store/store';
import { Toast } from 'toastify-react-native';

// Get current user data
const getCurrentUserData = () => {
  const userData = UserStore.getState().user;
  return {
    _id: userData?.id || '1',
    name: userData?.fullName || userData?.username || 'Me',
    avatar:
      userData?.profilePicture || 'https://images.unsplash.com/photo-1540575467063-178a50c2df87',
  };
};

const dummyCommunities = [
  {
    id: '1',
    name: 'Photography Lovers',
    avatar: 'https://images.unsplash.com/photo-1540575467063-178a50c2df87',
    memberCount: 124,
    lastMessage: 'Great shot! Where was that taken?',
    timestamp: '2h ago',
  },
  {
    id: '2',
    name: 'Local Meetups',
    avatar: 'https://images.unsplash.com/photo-1540575467063-178a50c2df87',
    memberCount: 56,
    lastMessage: "I'll be there at 7pm",
    timestamp: 'Yesterday',
  },
  {
    id: '3',
    name: 'Fitness Group',
    avatar: 'https://images.unsplash.com/photo-1540575467063-178a50c2df87',
    memberCount: 89,
    lastMessage: 'New workout plan posted!',
    timestamp: '3d ago',
  },
];

const dummyEvents = [
  {
    id: '1',
    name: 'Summer Music Festival',
    date: 'Aug 15',
    avatar: 'https://images.unsplash.com/photo-1540575467063-178a50c2df87',
    attendeeCount: 320,
    lastMessage: "Who's going to the main stage?",
    timestamp: '5m ago',
    unread: 2,
  },
  {
    id: '2',
    name: 'Tech Conference 2023',
    date: 'Sep 20',
    avatar: 'https://images.unsplash.com/photo-1540575467063-178a50c2df87',
    attendeeCount: 175,
    lastMessage: 'Panel discussion starts in 10 minutes',
    timestamp: '2h ago',
  },
  {
    id: '3',
    name: 'Art Exhibition',
    date: 'Jul 25',
    avatar: 'https://images.unsplash.com/photo-1540575467063-178a50c2df87',
    attendeeCount: 82,
    lastMessage: 'Does anyone know if photos are allowed?',
    timestamp: 'Jan 20',
  },
];

// Direct chats will now come from Firebase
// const directChats = [];

// Mock messages
const generateMessages = (count, offset = 0) => {
  return Array(count)
    .fill(0)
    .map((_, i) => ({
      _id: i + offset,
      text: `Message ${i + offset}`,
      createdAt: new Date(Date.now() - (i + offset) * 60 * 1000),
      user: {
        _id: i % 2 === 0 ? 1 : 2,
        name: i % 2 === 0 ? 'Me' : 'John Doe',
        avatar:
          i % 2 === 0
            ? 'https://images.unsplash.com/photo-1540575467063-178a50c2df87'
            : 'https://images.unsplash.com/photo-1540575467063-178a50c2df87',
      },
    }));
};

// Enum for chat types
enum ChatType {
  DIRECT = 'direct',
  COMMUNITY = 'community',
  EVENT = 'event',
}

const Chat = () => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [chatType, setChatType] = useState<ChatType>(ChatType.DIRECT);
  const [selectedChat, setSelectedChat] = useState<ChatItem | null>(null);
  const [myCommunities, setMyCommunities] = useState(dummyCommunities);
  const [showCreateCommunityModal, setShowCreateCommunityModal] = useState(false);
  const [showBrowseCommunitiesModal, setShowBrowseCommunitiesModal] = useState(false);
  const [currentChatId, setCurrentChatId] = useState<string | null>(null);
  const [messageListener, setMessageListener] = useState<(() => void) | null>(null);
  const [showImagePreviewModal, setShowImagePreviewModal] = useState(false);
  const [selectedImages, setSelectedImages] = useState<string[]>([]);
  const [imageCaption, setImageCaption] = useState('');
  const [tempMessages, setTempMessages] = useState<any[]>([]);
  const [messagesLoading, setMessagesLoading] = useState(false);

  const router = useRouter();
  const insets = useSafeAreaInsets();
  const { colors, colorScheme, setColorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';
  const [keyboardVisible, setKeyboardVisible] = useState(false);
  const userData = UserStore((state) => state.user);

  // Firebase chat hook
  const {
    chats,
    loading,
    error,
    friendsWithoutChats,
    loadChats,
    startChat,
    sendMessage: sendFirebaseMessage,
    listenToMessages,
    updateChatLastMessage,
  } = useFirebaseChat();

  // Show error toast when Firebase error occurs
  useEffect(() => {
    if (error) {
      Toast.show({
        type: 'error',
        text1: 'Chat Error',
        text2: error,
        position: 'bottom',
        theme: isDark ? 'dark' : 'light',
        backgroundColor: colors.background,
        autoHide: true,
      });
    }
  }, [error, isDark, colors]);

  // Reference for the bottom sheet
  const newChatSheetRef = useRef<BottomSheet>(null);
  // Define the snap points for the bottom sheet
  const snapPoints = useMemo(() => ['70%'], []);

  // Open the new chat bottom sheet
  const handleOpenNewChatSheet = useCallback(() => {
    newChatSheetRef.current?.expand();
  }, []);

  // Close the new chat bottom sheet
  const handleCloseNewChatSheet = useCallback(() => {
    newChatSheetRef.current?.close();
  }, []);

  // Listen to messages when a chat is selected
  useEffect(() => {
    if (selectedChat && selectedChat.chatId) {
      // Set loading to true when starting to fetch messages
      setMessagesLoading(true);

      // Clean up previous listener
      if (messageListener) {
        messageListener();
      }

      // Set up new listener
      const unsubscribe = listenToMessages(selectedChat.chatId, (newMessages) => {
        setMessages(newMessages);
        // Set loading to false once messages are received
        setMessagesLoading(false);

        // Clear all temporary messages when Firebase updates
        setTempMessages([]);
      });

      setMessageListener(() => unsubscribe);
      setCurrentChatId(selectedChat.chatId);
    } else {
      // Clean up listener when no chat is selected
      if (messageListener) {
        messageListener();
        setMessageListener(null);
      }
      setMessages([]);
      setMessagesLoading(false);
      setCurrentChatId(null);
    }

    // Cleanup on unmount
    return () => {
      if (messageListener) {
        messageListener();
      }
    };
  }, [selectedChat, listenToMessages]);

  const handleSelectChat = (chat: ChatItem, type: 'direct' | 'community' | 'event') => {
    setChatType(type as ChatType);

    setSelectedChat(chat);
  };

  const handleSelectFriend = async (friend: Friend) => {
    try {
      // Start a new chat with Firebase
      const chatId = await startChat(friend.id);

      if (chatId) {
        // Create a new chat item from the friend
        const newChat: ChatItem = {
          id: friend.id,
          name: friend.name,
          avatar: friend.avatar,
          lastMessage: '',
          timestamp: 'Just now',
          chatId: chatId,
        };

        // Close the bottom sheet
        handleCloseNewChatSheet();

        // Start the chat
        handleSelectChat(newChat, 'direct');
      } else {
        Toast.show({
          type: 'error',
          text1: 'Error',
          text2: 'Failed to start chat',
          position: 'bottom',
          theme: isDark ? 'dark' : 'light',
          backgroundColor: colors.background,
          autoHide: true,
        });
      }
    } catch (error) {
      console.error('Error starting chat:', error);
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Failed to start chat',
        position: 'bottom',
        theme: isDark ? 'dark' : 'light',
        backgroundColor: colors.background,
        autoHide: true,
      });
    }
  };

  const handleOpenCreateCommunity = () => {
    setShowCreateCommunityModal(true);
  };

  const handleCloseCreateCommunity = () => {
    setShowCreateCommunityModal(false);
  };

  const handleCreateCommunity = (name: string, description: string, imageUri: string) => {
    // Create new community and add to myCommunities
    const newCommunity = {
      id: Math.floor(Math.random() * 10000).toString(),
      name,
      avatar: imageUri,
      memberCount: 1,
      lastMessage: 'Welcome to the community!',
      timestamp: 'Just now',
    };

    setMyCommunities([newCommunity, ...myCommunities]);
    setShowCreateCommunityModal(false);
  };

  const handleOpenBrowseCommunities = () => {
    setShowBrowseCommunitiesModal(true);
  };

  const handleCloseBrowseCommunities = () => {
    setShowBrowseCommunitiesModal(false);
  };

  const handleJoinCommunity = (community: any) => {
    // Check if community is already joined
    const isJoined = myCommunities.some((c) => c.id === community.id);

    if (!isJoined) {
      // Add the joined community to myCommunities
      const newCommunity = {
        id: community.id.toString(),
        name: community.name,
        avatar: community.avatar,
        memberCount: community.memberCount,
        lastMessage: 'Welcome to the community!',
        timestamp: 'Just now',
      };

      setMyCommunities([newCommunity, ...myCommunities]);
    }

    setShowBrowseCommunitiesModal(false);
  };

  const onSend = useCallback(
    async (newMessages: any[] = []) => {
      if (!currentChatId || !selectedChat || !userData) return;

      const message = newMessages[0];
      if (!message) return;

      try {
        // Handle multiple images
        if (message.images && Array.isArray(message.images)) {
          // Create temporary messages for each image
          const tempImageMessages = message.images.map((imageUri: string, index: number) => ({
            _id: `temp_${Math.round(Math.random() * 1000000)}_${index}`,
            text: index === message.images.length - 1 && message.text ? message.text : undefined,
            createdAt: new Date(Date.now() + index),
            user: getCurrentUserData(),
            image: imageUri,
            pending: true,
            failed: false,
          }));

          // Add temporary messages to display immediately
          setTempMessages((prev) => [...tempImageMessages, ...prev]);

          // Send each image (don't wait for response, rely on Firebase listener)
          for (let i = 0; i < message.images.length; i++) {
            const imageUri = message.images[i];
            const isLastImage = i === message.images.length - 1;
            const messageText = isLastImage && message.text ? message.text : undefined;
            const tempMessage = tempImageMessages[i];

            // Send without waiting for response
            sendFirebaseMessage(currentChatId, selectedChat.id, messageText, imageUri).catch(
              (error) => {
                console.error('Error sending image:', error);
                // Mark as failed only on error
                setTempMessages((prev) =>
                  prev.map((msg) =>
                    msg._id === tempMessage._id ? { ...msg, pending: false, failed: true } : msg
                  )
                );
              }
            );
          }
        } else {
          // Handle single message (text or single image)
          if (!message.text && !message.image) return;

          // Create temporary message
          const tempMessage = {
            ...message,
            _id: `temp_${Math.round(Math.random() * 1000000)}`,
            pending: true,
            failed: false,
          };

          // Add temporary message to display immediately
          setTempMessages((prev) => [tempMessage, ...prev]);

          // Send message (don't wait for response, rely on Firebase listener)
          sendFirebaseMessage(currentChatId, selectedChat.id, message.text, message.image).catch(
            (error) => {
              console.error('Error sending message:', error);
              // Mark as failed only on error
              setTempMessages((prev) =>
                prev.map((msg) =>
                  msg._id === tempMessage._id ? { ...msg, pending: false, failed: true } : msg
                )
              );
            }
          );
        }
      } catch (error) {
        console.error('Error sending message:', error);
        Toast.show({
          type: 'error',
          text1: 'Error',
          text2: 'Failed to send message',
          position: 'bottom',
          theme: isDark ? 'dark' : 'light',
          backgroundColor: colors.background,
          autoHide: true,
        });
      }
    },
    [currentChatId, selectedChat, userData, sendFirebaseMessage, isDark, colors]
  );

  const handleRetryMessage = useCallback(
    async (tempMessage: any) => {
      if (!currentChatId || !selectedChat) return;

      // Mark as pending again
      setTempMessages((prev) =>
        prev.map((msg) =>
          msg._id === tempMessage._id ? { ...msg, pending: true, failed: false } : msg
        )
      );

      // Retry sending (don't wait for response, rely on Firebase listener)
      sendFirebaseMessage(
        currentChatId,
        selectedChat.id,
        tempMessage.text,
        tempMessage.image
      ).catch((error) => {
        console.error('Error retrying message:', error);
        // Mark as failed again only on error
        setTempMessages((prev) =>
          prev.map((msg) =>
            msg._id === tempMessage._id ? { ...msg, pending: false, failed: true } : msg
          )
        );
      });
    },
    [currentChatId, selectedChat, sendFirebaseMessage]
  );

  const handleSendImagesWithCaption = useCallback(() => {
    if (selectedImages.length === 0) return;

    // Create a message with multiple images
    const newMessage = {
      _id: Math.round(Math.random() * 1000000).toString(),
      createdAt: new Date(),
      user: getCurrentUserData(),
      images: selectedImages, // Send as array
      text: imageCaption.trim() || undefined,
    };

    onSend([newMessage]);

    // Reset modal state
    setShowImagePreviewModal(false);
    setSelectedImages([]);
    setImageCaption('');
  }, [selectedImages, imageCaption, onSend]);

  const handleCloseImagePreview = useCallback(() => {
    setShowImagePreviewModal(false);
    setSelectedImages([]);
    setImageCaption('');
  }, []);

  const handleRemoveImage = useCallback((index: number) => {
    setSelectedImages((prev) => prev.filter((_, i) => i !== index));
  }, []);

  const renderActions = (props: any) => {
    return (
      <Actions
        {...props}
        containerStyle={{
          marginLeft: 5,
          marginBottom: 8,
          justifyContent: 'center',
          alignItems: 'center',
          height: 36,
          width: 36,
          backgroundColor: colors.background,
        }}
        icon={() => (
          <Ionicons name="add-circle-outline" className="" size={40} color={colors.primary} />
        )}
        options={{
          'Choose From Library': async () => {
            try {
              const images = await ImagePicker.openPicker({
                mediaType: 'any',
                multiple: true,
                maxFiles: 10,
                compressImageQuality: 0.8,
                includeBase64: false,
              });

              if (images && images.length > 0) {
                const imageUris = images.map((image) => image.path);
                setSelectedImages(imageUris);
                setShowImagePreviewModal(true);
              }
            } catch (error) {
              console.log('Error picking images:', error);
            }
          },
          'Take a Photo': async () => {
            try {
              const image = await ImagePicker.openCamera({
                width: 800,
                height: 600,
                cropping: true,
                compressImageQuality: 0.8,
                includeBase64: false,
              });

              setSelectedImages([image.path]);
              setShowImagePreviewModal(true);
            } catch (error: any) {
              if (error.code !== 'E_PICKER_CANCELLED') {
                alert('Sorry, we need camera permissions to make this work!');
              }
            }
          },
          'Record a Video': async () => {
            try {
              const video = await ImagePicker.openCamera({
                mediaType: 'video',
                compressVideoPreset: 'MediumQuality',
                includeBase64: false,
              });

              const newMessage = {
                _id: Math.round(Math.random() * 1000000).toString(),
                createdAt: new Date(),
                user: getCurrentUserData(),
                video: video.path,
              };
              onSend([newMessage]);
            } catch (error: any) {
              if (error.code !== 'E_PICKER_CANCELLED') {
                alert('Sorry, we need camera permissions to make this work!');
              }
            }
          },
          Cancel: () => {},
        }}
      />
    );
  };

  // Combine real messages with temporary messages
  const allMessages = useMemo(() => {
    return [...tempMessages, ...messages];
  }, [tempMessages, messages]);

  // Tabs component
  const renderTabs = () => {
    return (
      <View
        className="flex-row justify-around border-b"
        style={{ borderBottomColor: 'rgba(150, 150, 150, 0.2)' }}>
        <TouchableOpacity
          className={`flex-1 items-center border-b-2 py-3.5 ${chatType === ChatType.DIRECT ? 'border-violet-600' : 'border-transparent'}`}
          onPress={() => setChatType(ChatType.DIRECT)}>
          <Text
            className={`font-medium text-xs ${chatType === ChatType.DIRECT ? 'opacity-100' : 'opacity-70'}`}
            style={{ color: chatType === ChatType.DIRECT ? colors.foreground : colors.grey }}>
            DIRECT
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          className={`flex-1 items-center border-b-2 py-3.5 ${chatType === ChatType.EVENT ? 'border-violet-600' : 'border-transparent'}`}
          onPress={() => setChatType(ChatType.EVENT)}>
          <Text
            className={`font-medium text-xs ${chatType === ChatType.EVENT ? 'opacity-100' : 'opacity-70'}`}
            style={{ color: chatType === ChatType.EVENT ? colors.foreground : colors.grey }}>
            EVENTS
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          className={`flex-1 items-center border-b-2 py-3.5 ${chatType === ChatType.COMMUNITY ? 'border-violet-600' : 'border-transparent'}`}
          onPress={() => setChatType(ChatType.COMMUNITY)}>
          <Text
            className={`font-medium text-xs ${chatType === ChatType.COMMUNITY ? 'opacity-100' : 'opacity-70'}`}
            style={{ color: chatType === ChatType.COMMUNITY ? colors.foreground : colors.grey }}>
            COMMUNITIES
          </Text>
        </TouchableOpacity>
      </View>
    );
  };

  const renderBackdrop = useCallback(
    (props) => (
      <BottomSheetBackdrop {...props} disappearsOnIndex={-1} appearsOnIndex={0} opacity={0.5} />
    ),
    []
  );

  // Render chat lists based on type
  const renderChatLists = () => {
    let data = [];
    let renderType = 'direct';
    let headerTitle = '';
    let showAddButton = false;

    switch (chatType) {
      case ChatType.DIRECT:
        data = chats; // Use Firebase chats
        renderType = 'direct';
        headerTitle = 'Direct Messages';
        showAddButton = true;
        break;
      case ChatType.COMMUNITY:
        data = myCommunities;
        renderType = 'community';
        headerTitle = 'Communities';
        break;
      case ChatType.EVENT:
        data = dummyEvents;
        renderType = 'event';
        headerTitle = 'Event Chats';
        break;
    }

    return (
      <View className="flex-1" style={{ backgroundColor: colors.background }}>
        <View className="mt-10"></View>

        {renderTabs()}

        {/* Show loader when loading direct chats */}
        {loading && chatType === ChatType.DIRECT ? (
          <View className="flex-1 items-center justify-center">
            <ActivityIndicator size="large" color={colors.primary} />
            <Text className="mt-4 text-base opacity-70" style={{ color: colors.foreground }}>
              Loading chats...
            </Text>
          </View>
        ) : (
          <ChatList
            data={data}
            type={renderType as 'direct' | 'community' | 'event'}
            onSelect={handleSelectChat}
            isDark={isDark}
            colors={colors}
            headerTitle={headerTitle}
            showAddButton={showAddButton}
            onAddPress={
              renderType === 'community' ? handleOpenCreateCommunity : handleOpenNewChatSheet
            }
            onBrowsePress={handleOpenBrowseCommunities}
          />
        )}
      </View>
    );
  };

  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener('keyboardDidShow', () =>
      setKeyboardVisible(true)
    );
    const keyboardDidHideListener = Keyboard.addListener('keyboardDidHide', () =>
      setKeyboardVisible(false)
    );

    return () => {
      keyboardDidShowListener.remove();
      keyboardDidHideListener.remove();
    };
  }, []);

  return (
    <SafeAreaView
      className="flex-1 pt-12"
      style={{
        backgroundColor: colors.background,
      }}>
      <StatusBar style={isDark ? 'light' : 'dark'} />
      {selectedChat ? (
        <></>
      ) : (
        <View className="flex-row items-center justify-center px-4 pb-4">
          <Text className={`font-medium text-xl ${isDark ? 'text-white' : 'text-black'}`}>
            Messages
          </Text>
        </View>
      )}
      {selectedChat ? (
        <ActiveChat
          selectedChat={selectedChat}
          messages={allMessages}
          onSend={onSend}
          onBack={() => setSelectedChat(null)}
          renderActions={renderActions}
          colors={colors}
          isDark={isDark}
          user={getCurrentUserData()}
          onRetryMessage={handleRetryMessage}
          messagesLoading={messagesLoading}
        />
      ) : (
        renderChatLists()
      )}

      {/* Image Preview Modal */}
      <Modal
        visible={showImagePreviewModal}
        animationType="slide"
        presentationStyle="fullScreen"
        onRequestClose={handleCloseImagePreview}>
        <KeyboardAvoidingView
          style={{ flex: 1 }}
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
          keyboardVerticalOffset={0}>
          <View style={{ flex: 1, backgroundColor: 'black' }}>
            {/* Header */}
            <SafeAreaView>
              <View className="flex-row items-center justify-between px-4 py-2">
                <TouchableOpacity onPress={handleCloseImagePreview}>
                  <Ionicons name="close" size={24} color="white" />
                </TouchableOpacity>
                <Text className="font-medium text-white">
                  Send {selectedImages.length} {selectedImages.length === 1 ? 'Image' : 'Images'}
                </Text>
                <View style={{ width: 24 }} />
              </View>
            </SafeAreaView>

            {/* Images Preview */}
            <View className="flex-1 items-center justify-center">
              {selectedImages.length === 1 ? (
                // Single image - full screen
                <Image
                  source={{ uri: selectedImages[0] }}
                  style={{
                    width: Dimensions.get('window').width,
                    height: Dimensions.get('window').height * 0.7,
                  }}
                  resizeMode="contain"
                />
              ) : (
                // Multiple images - grid view
                <View style={{ flex: 1, paddingHorizontal: 16 }}>
                  <Text className="mb-4 text-center text-white">
                    {selectedImages.length} images selected
                  </Text>
                  <View
                    style={{
                      flexDirection: 'row',
                      flexWrap: 'wrap',
                      justifyContent: 'center',
                      gap: 8,
                    }}>
                    {selectedImages.map((uri, index) => (
                      <View key={index} style={{ position: 'relative' }}>
                        <Image
                          source={{ uri }}
                          style={{
                            width: 100,
                            height: 100,
                            borderRadius: 8,
                          }}
                          resizeMode="cover"
                        />
                        <TouchableOpacity
                          onPress={() => handleRemoveImage(index)}
                          style={{
                            position: 'absolute',
                            top: -8,
                            right: -8,
                            backgroundColor: 'red',
                            borderRadius: 12,
                            width: 24,
                            height: 24,
                            justifyContent: 'center',
                            alignItems: 'center',
                          }}>
                          <Ionicons name="close" size={16} color="white" />
                        </TouchableOpacity>
                      </View>
                    ))}
                  </View>
                </View>
              )}
            </View>

            {/* Caption Input and Send Button */}
            <SafeAreaView>
              <View
                className="flex-row items-end px-4 py-2"
                style={{
                  backgroundColor: 'rgba(0,0,0,0.8)',
                  marginBottom: keyboardVisible ? 23 : 0,
                }}>
                <View className="mr-3 flex-1">
                  <TextInput
                    value={imageCaption}
                    onChangeText={setImageCaption}
                    placeholder="Add a caption..."
                    placeholderTextColor="#999"
                    multiline
                    maxLength={500}
                    style={{
                      backgroundColor: 'rgba(255,255,255,0.1)',
                      borderRadius: 20,
                      paddingHorizontal: 16,
                      paddingVertical: 12,
                      color: 'white',
                      fontSize: 16,
                      maxHeight: 100,
                    }}
                  />
                </View>
                <TouchableOpacity
                  onPress={handleSendImagesWithCaption}
                  className="h-12 w-12 items-center justify-center rounded-full"
                  style={{ backgroundColor: colors.primary }}>
                  <Ionicons name="send" size={20} color="white" />
                </TouchableOpacity>
              </View>
            </SafeAreaView>
          </View>
        </KeyboardAvoidingView>
      </Modal>

      {/* Bottom Sheet for New Chat */}
      <BottomSheet
        ref={newChatSheetRef}
        index={-1}
        snapPoints={snapPoints}
        enablePanDownToClose
        backdropComponent={renderBackdrop}
        handleIndicatorStyle={{
          backgroundColor: isDark ? '#6b7280' : '#a1a1aa',
          width: 40,
        }}
        backgroundStyle={{
          backgroundColor: colors.background,
        }}>
        <NewChatSheet
          bottomSheetRef={newChatSheetRef}
          onSelectFriend={handleSelectFriend}
          colors={colors}
          isDark={isDark}
          friendsWithoutChats={friendsWithoutChats}
        />
      </BottomSheet>

      {/* Modal for Creating Community */}
      <CreateCommunityModal
        visible={showCreateCommunityModal}
        onClose={handleCloseCreateCommunity}
        onCreateCommunity={handleCreateCommunity}
        colors={colors}
        isDark={isDark}
      />

      {/* Modal for Browsing Communities */}
      <BrowseCommunitiesModal
        visible={showBrowseCommunitiesModal}
        onClose={handleCloseBrowseCommunities}
        onJoinCommunity={handleJoinCommunity}
        colors={colors}
      />
    </SafeAreaView>
  );
};

export default Chat;
