import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Image,
  FlatList,
  ActivityIndicator,
  Alert,
} from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { useNavigation, useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { useColorScheme } from '~/lib/useColorScheme';
import { FriendService } from '~/services/FriendService';
import { UserStore } from '~/store/store';
import { Toast } from 'toastify-react-native';
import { defaultProfileImage } from '~/assets/Pins';

interface FriendRequest {
  id: string;
  requesterId: string;
  requesterName: string;
  requesterEmail: string;
  requesterAvatar: string;
  createdAt: string;
  status: string;
}

export default function ReceivedRequestsScreen() {
  const router = useRouter();
  const { colors, colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';
  const user = UserStore((state: any) => state.user);

  const [requests, setRequests] = useState<FriendRequest[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [processingRequests, setProcessingRequests] = useState<Set<string>>(new Set());

  useEffect(() => {
    fetchRequests();
  }, [user?.id]);

  const fetchRequests = async () => {
    if (!user?.id) return;

    try {
      setLoading(true);
      const response = await FriendService.getFriendRequests(user.id);

      if (
        response.success &&
        response.body &&
        Array.isArray(response.body) &&
        response.body.length > 0
      ) {
        const requestsData = response.body
          .filter((request: any) => request.status === 'PENDING')
          .map((request: any) => ({
            id: request.id.toString(),
            requesterId: request.requester.id.toString(),
            requesterName:
              request.requester?.fullName || request.requester?.username || 'Unknown User',
            requesterEmail: request.requester?.email || '', // Add email field
            requesterAvatar:
              (request.requester?.profilePicture?.length > 0
                ? request.requester.profilePicture[request.requester.profilePicture.length - 1]
                    ?.secureUrl
                : null) ||
              'https://res.cloudinary.com/dil9qrpak/image/upload/v1748976473/user-circles-set_hrudqa.png',
            createdAt: new Date(request.createdAt).toLocaleDateString(),
            status: request.status,
          }));
        setRequests(requestsData);
      } else {
        setRequests([]);
      }
    } catch (error) {
      console.error('Error fetching friend requests:', error);
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Failed to load friend requests',
        position: 'bottom',
        theme: isDark ? 'dark' : 'light',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await fetchRequests();
    setRefreshing(false);
  };

  const handleAcceptRequest = async (requestId: string, requesterName: string) => {
    Alert.alert('Accept Friend Request', `Accept friend request from ${requesterName}?`, [
      { text: 'Cancel', style: 'cancel' },
      {
        text: 'Accept',
        onPress: () => processRequest(requestId, 'ACCEPTED'),
      },
    ]);
  };

  const handleRejectRequest = async (requestId: string, requesterName: string) => {
    Alert.alert('Reject Friend Request', `Reject friend request from ${requesterName}?`, [
      { text: 'Cancel', style: 'cancel' },
      {
        text: 'Reject',
        style: 'destructive',
        onPress: () => processRequest(requestId, 'REJECTED'),
      },
    ]);
  };

  const processRequest = async (requestId: string, status: 'ACCEPTED' | 'REJECTED') => {
    try {
      setProcessingRequests((prev) => new Set(prev).add(requestId));

      const response = await FriendService.acceptFriendRequest(requestId, status);
      console.log('Response:', response);
      if (response.success) {
        Toast.show({
          type: 'success',
          text1: status === 'ACCEPTED' ? 'Request Accepted' : 'Request Rejected',
          text2: `Friend request ${status} successfully`,
          position: 'bottom',
          theme: isDark ? 'dark' : 'light',
        });

        // Remove the request from the list
        setRequests((prev) => prev.filter((req) => req.id !== requestId));
      }
    } catch (error) {
      console.error('Error processing friend request:', error);
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: `Failed to ${status === 'ACCEPTED' ? 'accept' : 'reject'} request`,
        position: 'bottom',
        theme: isDark ? 'dark' : 'light',
      });
    } finally {
      setProcessingRequests((prev) => {
        const newSet = new Set(prev);
        newSet.delete(requestId);
        return newSet;
      });
    }
  };

  const renderRequestItem = ({ item }: { item: FriendRequest }) => {
    const isProcessing = processingRequests.has(item.id);

    return (
      <View
        className="flex-row items-center border-b px-4 py-4"
        style={{ borderBottomColor: colors.grey5 }}>
        <TouchableOpacity
          onPress={() => {
            router.push({
              pathname: '/Auth/viewProfile',
              params: {
                userId: item.requesterId,
                email: item.requesterEmail, // Use actual email instead of name
              },
            });
          }}>
          <Image source={{ uri: item.requesterAvatar }} className="h-14 w-14 rounded-full" />
        </TouchableOpacity>
        <View className="ml-3 flex-1">
          <Text
            className={`font-medium text-base ${isDark ? 'text-white' : 'text-black'}`}
            numberOfLines={1}>
            {item.requesterName}
          </Text>
          <Text className={`text-xs ${isDark ? 'text-gray-400' : 'text-gray-500'}`}>
            {item.createdAt}
          </Text>
        </View>
        <View className="flex-row">
          <TouchableOpacity
            className="mr-2 rounded-lg px-4 py-2"
            style={{
              backgroundColor: colors.primary,
              opacity: isProcessing ? 0.5 : 1,
            }}
            onPress={() => handleAcceptRequest(item.id, item.requesterName)}
            disabled={isProcessing}>
            {isProcessing ? (
              <ActivityIndicator size="small" color="white" />
            ) : (
              <Text className="font-medium text-white">Accept</Text>
            )}
          </TouchableOpacity>
          <TouchableOpacity
            className="rounded-lg px-4 py-2"
            style={{
              backgroundColor: colors.grey5,
              opacity: isProcessing ? 0.5 : 1,
            }}
            onPress={() => handleRejectRequest(item.id, item.requesterName)}
            disabled={isProcessing}>
            <Text className={`font-medium ${isDark ? 'text-white' : 'text-black'}`}>Reject</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  return (
    <View className="flex-1" style={{ backgroundColor: colors.background }}>
      <StatusBar style={isDark ? 'light' : 'dark'} />

      <View className="flex-row items-center justify-between px-4 pb-4 pt-12">
        <TouchableOpacity className="p-2" onPress={() => router.replace('/(drawer)/friends')}>
          <Ionicons name="arrow-back" size={24} color={isDark ? '#fff' : '#000'} />
        </TouchableOpacity>
        <Text className={`font-medium text-lg ${isDark ? 'text-white' : 'text-black'}`}>
          Received Requests
        </Text>
        <View className="w-10" />
      </View>

      {loading ? (
        <View className="flex-1 items-center justify-center">
          <ActivityIndicator size="large" color={colors.primary} />
          <Text className={`mt-4 ${isDark ? 'text-white' : 'text-black'}`}>
            Loading requests...
          </Text>
        </View>
      ) : requests.length > 0 ? (
        <FlatList
          data={requests}
          renderItem={renderRequestItem}
          keyExtractor={(item) => item.id}
          contentContainerStyle={{ paddingBottom: 40 }}
          showsVerticalScrollIndicator={false}
          refreshing={refreshing}
          onRefresh={handleRefresh}
        />
      ) : (
        <View className="flex-1 items-center justify-center px-4">
          <Ionicons name="mail-outline" size={60} color={isDark ? colors.grey3 : colors.grey4} />
          <Text
            className={`mt-4 text-center font-medium text-lg ${isDark ? 'text-white' : 'text-black'}`}>
            No Friend Requests
          </Text>
          <Text className={`mt-2 text-center ${isDark ? 'text-gray-400' : 'text-gray-500'}`}>
            You don't have any pending friend requests
          </Text>
        </View>
      )}
    </View>
  );
}
