import React from 'react';
import { View, Text, SafeAreaView, ScrollView, TouchableOpacity, Platform } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { useRouter } from 'expo-router';
import { AntDesign, FontAwesome5 } from '@expo/vector-icons';
import { useColorScheme } from '~/lib/useColorScheme';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

export default function TermsAndConditionsScreen() {
  const router = useRouter();
  const { colors, colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';
  const insets = useSafeAreaInsets();
  return (
    <View
      className="flex-1"
      style={{
        paddingTop: Platform.OS === 'ios' ? insets.top : insets.top + 15,
        backgroundColor: colors.background,
      }}>
      <StatusBar style={isDark ? 'light' : 'dark'} />

      <View className="flex-row items-center justify-between px-3 pb-2">
        <TouchableOpacity
          onPress={() => router.back()}
          className="items-center justify-center w-10 h-10 rounded-full">
          <AntDesign name="arrowleft" size={24} color={colors.foreground} />
        </TouchableOpacity>
        <Text className="text-lg font-medium" style={{ color: colors.foreground }}>
          Terms & Conditions
        </Text>
        <View className="w-10" />
      </View>

      <ScrollView className="flex-1 px-6 pt-4" showsVerticalScrollIndicator={true}>
        <Text style={{ color: colors.foreground }} className="mb-6 text-2xl font-bold">
          Terms and Conditions
        </Text>

        <Text style={{ color: colors.foreground }} className="mb-2 text-lg font-medium">
          1. Acceptance of Terms
        </Text>
        <Text style={{ color: colors.foreground }} className="mb-4 text-base">
          By accessing or using our Social Events application, you agree to be bound by these Terms
          and Conditions. If you disagree with any part of the terms, you may not access the
          service.
        </Text>

        <Text style={{ color: colors.foreground }} className="mb-2 text-lg font-medium">
          2. User Accounts
        </Text>
        <Text style={{ color: colors.foreground }} className="mb-4 text-base">
          When you create an account with us, you must provide accurate, complete, and updated
          information. You are responsible for safeguarding the password and for all activities that
          occur under your account.
        </Text>

        <Text style={{ color: colors.foreground }} className="mb-2 text-lg font-medium">
          3. Content
        </Text>
        <Text style={{ color: colors.foreground }} className="mb-4 text-base">
          Our Service allows you to post, link, store, share and otherwise make available certain
          information, text, graphics, videos, or other material. You are responsible for the
          content you post, including its legality, reliability, and appropriateness.
        </Text>

        <Text style={{ color: colors.foreground }} className="mb-2 text-lg font-medium">
          4. Privacy Policy
        </Text>
        <Text style={{ color: colors.foreground }} className="mb-4 text-base">
          Your privacy is important to us. Our Privacy Policy explains how we collect, use, protect,
          and when we share personal information and other data with others. By using our Service,
          you agree to the collection and use of information in accordance with this policy.
        </Text>

        <Text style={{ color: colors.foreground }} className="mb-2 text-lg font-medium">
          5. Location Data
        </Text>
        <Text style={{ color: colors.foreground }} className="mb-4 text-base">
          Our app uses location data to enhance your experience. We only collect location data with
          your explicit permission and use it to show nearby events and users. You can disable
          location services at any time through your device settings.
        </Text>

        <Text style={{ color: colors.foreground }} className="mb-2 text-lg font-medium">
          6. Media Access
        </Text>
        <Text style={{ color: colors.foreground }} className="mb-4 text-base">
          We request access to your device's media gallery to allow you to upload and share photos
          related to events. We will never access your media without your explicit action to select
          and share content.
        </Text>

        <Text style={{ color: colors.foreground }} className="mb-2 text-lg font-medium">
          7. Events and Bookings
        </Text>
        <Text style={{ color: colors.foreground }} className="mb-4 text-base">
          Our platform allows users to create, discover, and book events. We are not responsible for
          the accuracy of event details, fulfillment of events, or quality of events posted by
          users. All transactions are final and subject to the event organizer's refund policy.
        </Text>

        <Text style={{ color: colors.foreground }} className="mb-2 text-lg font-medium">
          8. Termination
        </Text>
        <Text style={{ color: colors.foreground }} className="mb-4 text-base">
          We may terminate or suspend your account immediately, without prior notice or liability,
          for any reason whatsoever, including without limitation if you breach the Terms.
        </Text>

        <Text style={{ color: colors.foreground }} className="mb-2 text-lg font-medium">
          9. Changes to Terms
        </Text>
        <Text style={{ color: colors.foreground }} className="mb-4 text-base">
          We reserve the right, at our sole discretion, to modify or replace these Terms at any
          time. If a revision is material we will try to provide at least 30 days' notice prior to
          any new terms taking effect.
        </Text>

        <Text style={{ color: colors.foreground }} className="mb-2 text-lg font-medium">
          10. Contact Us
        </Text>
        <Text style={{ color: colors.foreground }} className="mb-8 text-base">
          If you have any questions about these Terms, please contact us at
          <EMAIL>.
        </Text>

        {/* Add some bottom padding for better scrolling experience */}
        <View className="h-8" />
      </ScrollView>
    </View>
  );
}
