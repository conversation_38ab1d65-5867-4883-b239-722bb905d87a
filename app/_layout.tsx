import '../global.css';
import 'expo-dev-client';

import { ActionSheetProvider } from '@expo/react-native-action-sheet';
import { BottomSheetModalProvider } from '@gorhom/bottom-sheet';
import { ThemeProvider as NavThemeProvider } from '@react-navigation/native';
import { useFonts } from 'expo-font';
import { Stack, useRouter } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { GestureHandlerRootView, Pressable } from 'react-native-gesture-handler';

import { ThemeToggle } from '~/components/ThemeToggle';
import { useColorScheme, useInitialAndroidBarSync } from '~/lib/useColorScheme';
import { NAV_THEME } from '~/theme';
import { Feather } from '@expo/vector-icons';
import ToastManager, { Toast } from 'toastify-react-native';
import { AuthStore } from '~/store/store';
import { useEffect } from 'react';
import { KeyboardProvider } from 'react-native-keyboard-controller';

export {
  // Catch any errors thrown by the Layout component.
  ErrorBoundary,
} from 'expo-router';

export default function RootLayout() {
  useInitialAndroidBarSync();
  const { colorScheme, isDarkColorScheme, isDark, colors } = useColorScheme();
  const router = useRouter();
  const isLoggedIn = AuthStore((state) => state.isLoggedIn);
  const permissions = AuthStore((state) => state.permissions);
  useEffect(() => {}, [isLoggedIn, permissions]);
  let [fontsLoaded] = useFonts({
    Regular: require('../theme/fonts/ios-font/Regular.otf'),

    Bold: require('../theme/fonts/ios-font/Bold.otf'),
    Medium: require('../theme/fonts/ios-font/Medium.otf'),
  });

  if (!fontsLoaded) {
    return null; // Or a loading screen
  }

  return (
    <>
      <StatusBar
        key={`root-status-bar-${isDark ? 'light' : 'dark'}`}
        style={isDark ? 'light' : 'dark'}
      />
      <KeyboardProvider>
        <GestureHandlerRootView style={{ flex: 1 }}>
          <BottomSheetModalProvider>
            <ActionSheetProvider>
              <NavThemeProvider value={NAV_THEME[colorScheme]}>
                <ToastManager />
                <Stack screenOptions={SCREEN_OPTIONS}>
                  <Stack.Protected guard={isLoggedIn && permissions}>
                    <Stack.Screen name="(drawer)" options={DRAWER_OPTIONS} />

                    <Stack.Screen
                      name="Events/createEvent"
                      options={{
                        ...MODAL_OPTIONS,
                        headerTitleAlign: 'center',
                        headerStyle: {
                          backgroundColor: colors.background,
                        },

                        ...{
                          headerLeft: () => (
                            <Pressable
                              onPress={() => {
                                router.back();
                              }}>
                              <Feather name="arrow-left" size={24} color={colors.foreground} />
                            </Pressable>
                          ),
                        },
                      }}
                    />
                    <Stack.Screen
                      name="Events/eventCreated"
                      options={{
                        headerShown: false,
                      }}
                    />

                    <Stack.Screen
                      name="Events/promoteEvent"
                      options={{
                        headerShown: false,
                      }}
                    />
                    <Stack.Screen
                      name="Events/viewEvent"
                      options={{
                        headerShown: false,
                      }}
                    />
                    <Stack.Screen
                      name="Events/eventDashboard"
                      options={{
                        headerShown: false,
                      }}
                    />
                    <Stack.Screen
                      name="Events/eventPromotions"
                      options={{
                        headerShown: false,
                      }}
                    />
                    <Stack.Screen
                      name="Auth/editProfile"
                      options={{
                        headerShown: false,
                      }}
                    />
                    <Stack.Screen
                      name="Auth/viewProfile"
                      options={{
                        headerShown: false,
                      }}
                    />
                    <Stack.Screen
                      name="Auth/settings"
                      options={{
                        headerShown: false,
                      }}
                    />
                    <Stack.Screen name="settings" options={MODAL_OPTIONS_SETTINGS} />
                  </Stack.Protected>
                  <Stack.Screen name="Auth/login" options={{ headerShown: false }} />
                  <Stack.Screen name="Auth/permissions" options={{ headerShown: false }} />
                  <Stack.Screen name="index" options={{ headerShown: false }} />
                  <Stack.Screen name="Auth/forgotPassword" options={{ headerShown: false }} />
                  <Stack.Screen name="Auth/signup" options={{ headerShown: false }} />
                  <Stack.Screen name="Auth/onboarding" options={{ headerShown: false }} />
                  <Stack.Screen name="Auth/termsAndConditions" options={{ headerShown: false }} />
                  <Stack.Screen name="Auth/profileSetup" options={{ headerShown: false }} />
                </Stack>
              </NavThemeProvider>
            </ActionSheetProvider>
          </BottomSheetModalProvider>
        </GestureHandlerRootView>
      </KeyboardProvider>
    </>
  );
}

const SCREEN_OPTIONS = {
  animation: 'ios_from_right', // for android
} as const;

const DRAWER_OPTIONS = {
  headerShown: false,
} as const;

const MODAL_OPTIONS = {
  presentation: 'modal',
  animation: 'fade_from_bottom', // for android
  title: 'Create Event',
} as const;

const MODAL_OPTIONS_SETTINGS = {
  presentation: 'modal',
  animation: 'fade_from_bottom', // for android
  title: 'Settings',
} as const;
