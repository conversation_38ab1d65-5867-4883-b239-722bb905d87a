import * as SecureStore from 'expo-secure-store';
import axiosInstance from './config';

export class CommunityService {
  static async createCommunity(communityDetails: {
    userId: string;
    communityName: string;
    communityDescription: string;
    communityImages?: File[];
  }) {
    try {
      const formData = new FormData();
      formData.append('userId', communityDetails.userId);
      formData.append('communityName', communityDetails.communityName);
      formData.append('communityDescription', communityDetails.communityDescription);
      
      if (communityDetails.communityImages && communityDetails.communityImages.length > 0) {
        communityDetails.communityImages.forEach((image, index) => {
          formData.append('communityImages', {
            uri: image,
            name: `community_image_${index}.jpg`,
            type: 'image/jpeg',
          } as any);
        });
      }

      const token = await SecureStore.getItemAsync('accessToken');
      const response = await axiosInstance.post('/community/v1/create-community', formData, {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'multipart/form-data',
        },
      });

      const data = response.data;
      if (data.success) {
        return data;
      } else {
        throw new Error(data.message);
      }
    } catch (error) {
      throw error;
    }
  }

  static async joinCommunity(communityId: string, userId: string) {
    try {
      const token = await SecureStore.getItemAsync('accessToken');
      const response = await axiosInstance.post('/community/v1/join-community', {
        communityId,
        userId,
      }, {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      const data = response.data;
      if (data.success) {
        return data;
      } else {
        throw new Error(data.message);
      }
    } catch (error) {
      throw error;
    }
  }

  static async leaveCommunity(communityId: string, userId: string) {
    try {
      const token = await SecureStore.getItemAsync('accessToken');
      const response = await axiosInstance.post('/community/v1/leave-community', {
        communityId,
        userId,
      }, {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      const data = response.data;
      if (data.success) {
        return data;
      } else {
        throw new Error(data.message);
      }
    } catch (error) {
      throw error;
    }
  }

  static async getCommunities() {
    try {
      const token = await SecureStore.getItemAsync('accessToken');
      const response = await axiosInstance.get('/community/v1/communities', {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      const data = response.data;
      if (data.success) {
        return data;
      } else {
        throw new Error(data.message);
      }
    } catch (error) {
      throw error;
    }
  }

  static async getUserCommunities(userId: string) {
    try {
      const token = await SecureStore.getItemAsync('accessToken');
      const response = await axiosInstance.get(`/community/v1/user/${userId}/communities`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      const data = response.data;
      if (data.success) {
        return data;
      } else {
        throw new Error(data.message);
      }
    } catch (error) {
      throw error;
    }
  }
}
