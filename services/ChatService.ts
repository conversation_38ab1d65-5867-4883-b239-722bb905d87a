import * as SecureStore from 'expo-secure-store';
import axiosInstance from './config';

export class ChatService {
  static async createFirebaseUser(userDetails: {
    fullName: string;
    email: string;
    userId: string;
  }) {
    try {
      const formData = new FormData();
      formData.append('fullName', userDetails.fullName);
      formData.append('email', userDetails.email);
      formData.append('userId', userDetails.userId);
      const token = await SecureStore.getItemAsync('accessToken');
      const response = await axiosInstance.post('/chat/v1/create-firebase-user', formData, {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'multipart/form-data',
        },
      });

      const data = response.data;
      if (data.success) {
        return data;
      } else {
        throw new Error(data.message);
      }
    } catch (error) {
      throw error;
    }
  }

  static async createPrivateChat(userOneId: string, userTwoId: string) {
    try {
      const formData = new FormData();
      formData.append('userOneId', userOneId);
      formData.append('userTwoId', userTwoId);
      const token = await SecureStore.getItemAsync('accessToken');
      const response = await axiosInstance.post('/chat/v1/create-private-chat', formData, {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'multipart/form-data',
        },
      });

      const data = response.data;
      if (data.success) {
        return data;
      } else {
        throw new Error(data.message);
      }
    } catch (error) {
      throw error;
    }
  }

  static async sendMessage(
    imageFiles: File,
    chatId: string,
    senderId: string,
    receiverId: string,
    messageText: string
  ) {
    try {
      const token = await SecureStore.getItemAsync('accessToken');
      const formData = new FormData();
      console.log(imageFiles);
      if (imageFiles) {
        formData.append('messageUploads', {
          uri: imageFiles,
          name: chatId + 'chatImage.jpg',
          type: 'image/jpeg',
        } as any);
        formData.append('chatId', chatId);
        formData.append('senderId', senderId);
        formData.append('receiverId', receiverId);
        formData.append('messageText', messageText === undefined ? ' ' : messageText);
      } else {
        formData.append('chatId', chatId);
        formData.append('senderId', senderId);
        formData.append('receiverId', receiverId);
        formData.append('messageText', messageText === undefined ? ' ' : messageText);
      }

      console.log('send messages,ddldlld', formData.getAll('messageUploads'));
      const response = await axiosInstance.post('/chat/v1/send-message', formData, {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'multipart/form-data',
        },
      });

      console.log(response.data);

      const data = response.data;
      if (data.success) {
        return data;
      } else {
        throw new Error(data.message);
      }
    } catch (error) {
      throw error;
    }
  }
}
