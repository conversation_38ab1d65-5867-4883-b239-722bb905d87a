import { useState, useEffect, useCallback } from 'react';
import { Toast } from 'toastify-react-native';

import { ChatService } from '~/services/ChatService';
import { FirebaseService, FirebaseMessage } from '~/services/FirebaseService';
import { FriendService } from '~/services/FriendService';
import { UserService } from '~/services/UserService';
import { UserStore } from '~/store/store';
import { ChatItem, Message, FirebaseChatUser, Friend } from '~/types/chat_type';

export const useFirebaseChat = () => {
  const [chats, setChats] = useState<ChatItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [friendsWithoutChats, setFriendsWithoutChats] = useState<Friend[]>([]);
  const userData = UserStore((state: any) => state.user);

  // Track active listeners for cleanup
  const [chatListeners, setChatListeners] = useState<{ [chatId: string]: () => void }>({});

  // Format last message for display with proper image handling
  const formatLastMessage = useCallback(
    (message: FirebaseMessage, senderId: string): string => {
      if (!message) return 'No messages yet';

      const isCurrentUser = senderId === userData?.id;
      const prefix = isCurrentUser ? '' : '';

      if (message.uploadUrl) {
        // Has image
        if (
          message.messageText &&
          message.messageText.trim() &&
          message.messageText.trim() !== ' '
        ) {
          // Image with text
          return `${prefix}📷 ${message.messageText}`;
        } else {
          // Image without text
          return `${prefix}📷 ${isCurrentUser ? 'Image sent' : 'Image received'}`;
        }
      } else {
        // Text only
        return `${prefix}${message.messageText}`;
      }
    },
    [userData?.id]
  );

  // Update chat list with new last message
  const updateChatLastMessage = useCallback(
    (chatId: string, lastMessage: FirebaseMessage) => {
      setChats((prevChats) => {
        const updatedChats = prevChats.map((chat) => {
          if (chat.chatId === chatId) {
            return {
              ...chat,
              lastMessage: formatLastMessage(lastMessage, lastMessage.senderId),
              timestamp: new Date(lastMessage.messageTime).toLocaleTimeString(),
            };
          }
          return chat;
        });

        // Sort by most recent activity (using messageTime for accurate sorting)
        return updatedChats.sort((a, b) => {
          // We need to store the timestamp as actual time for sorting
          // For now, let's sort by timestamp string comparison (newest first)
          if (!a.timestamp) return 1;
          if (!b.timestamp) return -1;
          return b.timestamp.localeCompare(a.timestamp);
        });
      });
    },
    [formatLastMessage]
  );

  // Convert Firebase message to GiftedChat message format
  const convertFirebaseMessageToGiftedChat = (
    firebaseMessage: FirebaseMessage & { id: string },
    users: { [userId: string]: FirebaseChatUser }
  ): Message => {
    const user = users[firebaseMessage.senderId];
    return {
      _id: firebaseMessage.id,
      text: firebaseMessage.messageText,
      createdAt: new Date(firebaseMessage.messageTime),
      user: {
        _id: firebaseMessage.senderId,
        name: user?.name || 'Unknown User',
        avatar:
          user?.avatar ||
          'https://res.cloudinary.com/dil9qrpak/image/upload/v1748976473/user-circles-set_hrudqa.png',
      },
      ...(firebaseMessage.uploadUrl && { image: firebaseMessage.uploadUrl }),
    };
  };

  // Load user's chats
  const loadChats = useCallback(async () => {
    if (!userData?.id) return;

    try {
      setLoading(true);
      setError(null);

      // Get user's chat IDs from Firebase
      const userChats = await FirebaseService.getUserChats(userData.id);
      const chatItems: ChatItem[] = [];

      // For each chat, get the other user's info and last message
      for (const [otherUserId, chatId] of Object.entries(userChats)) {
        try {
          // Get other user's info from your backend
          const user = await FirebaseService.getUser(otherUserId);
          const otherUserResponse = await UserService.getOtherUser({ email: user.email });

          if (otherUserResponse.success) {
            const otherUser = otherUserResponse.body;

            // Get last message from Firebase
            const lastMessage = await FirebaseService.getLastMessage(chatId);

            chatItems.push({
              id: otherUserId,
              name: otherUser.fullName || otherUser.username || 'Unknown User',
              avatar:
                otherUser.profilePicture ||
                'https://res.cloudinary.com/dil9qrpak/image/upload/v1748976473/user-circles-set_hrudqa.png',
              lastMessage: lastMessage
                ? formatLastMessage(lastMessage, lastMessage.senderId)
                : 'No messages yet',
              timestamp: lastMessage ? new Date(lastMessage.messageTime).toLocaleTimeString() : '',
              chatId,
            });

            // Set up real-time listener for this chat's last message
            const unsubscribe = FirebaseService.listenToMessages(chatId, (messages) => {
              if (messages.length > 0) {
                const lastMsg = messages[messages.length - 1];
                updateChatLastMessage(chatId, lastMsg);
              }
            });

            // Store the unsubscribe function
            setChatListeners((prev) => ({
              ...prev,
              [chatId]: unsubscribe,
            }));
          }
        } catch (error) {
          console.error(`Error loading chat with user ${otherUserId}:`, error);
        }
      }

      // Sort chats by last message time (using actual timestamp for sorting)
      chatItems.sort((a, b) => {
        // For now, use string comparison (this will be updated by real-time listeners)
        if (!a.timestamp && !b.timestamp) return 0;
        if (!a.timestamp) return 1;
        if (!b.timestamp) return -1;
        return b.timestamp.localeCompare(a.timestamp);
      });

      setChats(chatItems);

      // Load friends without chats
      await loadFriendsWithoutChats(Object.keys(userChats));
    } catch (error) {
      console.error('Error loading chats:', error);
      setError('Failed to load chats');
    } finally {
      setLoading(false);
    }
  }, [userData?.id]);

  // Load friends who don't have existing chats
  const loadFriendsWithoutChats = useCallback(
    async (existingChatUserIds: string[]) => {
      if (!userData?.id) return;

      try {
        const response = await FriendService.getFriends(userData.id);
        if (response.success && response.body && Array.isArray(response.body)) {
          const allFriends = response.body.friends.map((friend: any) => ({
            id: friend.id.toString(),
            name: friend.fullName || friend.username || 'Unknown User',
            avatar:
              friend.profilePicture?.[0]?.secureUrl ||
              friend.profilePicture ||
              'https://res.cloudinary.com/dil9qrpak/image/upload/v1748976473/user-circles-set_hrudqa.png',
            status: friend.isOnline ? 'online' : 'offline',
            email: friend.email,
            fullName: friend.fullName,
          }));

          // Filter out friends who already have chats
          const friendsWithoutChats = allFriends.filter(
            (friend: Friend) => !existingChatUserIds.includes(friend.id)
          );

          setFriendsWithoutChats(friendsWithoutChats);
        } else {
          setFriendsWithoutChats([]);
        }
      } catch (error) {
        console.error('Error loading friends without chats:', error);
        setFriendsWithoutChats([]);
      }
    },
    [userData?.id]
  );

  // Start a new chat with a user
  const startChat = useCallback(
    async (otherUserId: string): Promise<string | null> => {
      if (!userData?.id) return null;

      try {
        // Create private chat using backend endpoint
        const response = await ChatService.createPrivateChat(userData.id, otherUserId);

        if (response.success && response.body) {
          // Reload chats to include the new one

          const userChats = await FirebaseService.getUserChats(userData.id);
          const chatId = userChats[otherUserId];
          await loadChats();
          return chatId;
        } else {
          throw new Error(response.message || 'Failed to create chat');
        }
      } catch (error) {
        console.error('Error starting chat:', error);

        // Check if the error is specifically about chat already existing
        const errorMessage = error instanceof Error ? error.message : String(error);
        if (errorMessage.includes('Chat already exists')) {
          // Get the existing chat ID using getUserChats
          try {
            const userChats = await FirebaseService.getUserChats(userData.id);
            const existingChatId = userChats[otherUserId];

            if (existingChatId) {
              // Show toast notification about existing chat
              Toast.show({
                type: 'info',
                text1: 'Chat Exists',
                text2: 'Redirecting to existing chat',
                position: 'bottom',
                autoHide: true,
              });

              // Reload chats to ensure we have the latest data
              await loadChats();
              return existingChatId;
            }
          } catch (getUserChatsError) {
            console.error('Error getting existing chat ID:', getUserChatsError);
          }
        }

        setError('Failed to start chat');
        return null;
      }
    },
    [userData?.id, loadChats]
  );

  // Send a message
  const sendMessage = useCallback(
    async (
      chatId: string,
      receiverId: string,
      messageText: string,
      imageFiles: File
    ): Promise<boolean> => {
      if (!userData?.id) return false;

      try {
        await ChatService.sendMessage(imageFiles, chatId, userData.id, receiverId, messageText);
        return true;
      } catch (error) {
        console.error('Error sending message:', error);
        setError('Failed to send message');
        return false;
      }
    },
    [userData?.id]
  );

  // Listen to messages in a specific chat
  const listenToMessages = useCallback(
    (chatId: string, callback: (messages: Message[]) => void) => {
      if (!chatId) return () => {};

      return FirebaseService.listenToMessages(chatId, async (firebaseMessages) => {
        try {
          // Get user info for all participants
          const userIds = [...new Set(firebaseMessages.map((msg) => msg.senderId))];
          const users: { [userId: string]: FirebaseChatUser } = {};

          // Load user data for message senders
          for (const userId of userIds) {
            try {
              if (userId === userData?.id) {
                // Current user
                users[userId] = {
                  id: userData.id,
                  name: userData.fullName || userData.username || 'You',
                  avatar:
                    userData.profilePicture ||
                    'https://res.cloudinary.com/dil9qrpak/image/upload/v1748976473/user-circles-set_hrudqa.png',
                  email: userData.email,
                  fullName: userData.fullName,
                };
              } else {
                // Other user
                const user = await FirebaseService.getUser(userId);
                const userResponse = await UserService.getOtherUser({ email: user.email });

                if (userResponse.success) {
                  const user = userResponse.body;
                  users[userId] = {
                    id: userId,
                    name: user.fullName || user.username || 'Unknown User',
                    avatar:
                      user.profilePicture ||
                      'https://res.cloudinary.com/dil9qrpak/image/upload/v1748976473/user-circles-set_hrudqa.png',
                    email: user.email,
                    fullName: user.fullName,
                  };
                }
              }
            } catch (error) {
              console.error(`Error loading user ${userId}:`, error);
              // Fallback user data
              users[userId] = {
                id: userId,
                name: 'Unknown User',
                avatar:
                  'https://res.cloudinary.com/dil9qrpak/image/upload/v1748976473/user-circles-set_hrudqa.png',
                email: '',
                fullName: 'Unknown User',
              };
            }
          }

          // Convert Firebase messages to GiftedChat format
          const giftedChatMessages = firebaseMessages.map((msg: any) =>
            convertFirebaseMessageToGiftedChat(msg, users)
          );

          // Reverse for GiftedChat (newest first)
          callback(giftedChatMessages.reverse());
        } catch (error) {
          console.error('Error processing messages:', error);
          callback([]);
        }
      });
    },
    [userData]
  );

  // Load chats on mount and when user changes
  useEffect(() => {
    if (userData?.id) {
      loadChats();
    }

    // Cleanup listeners on unmount or user change
    return () => {
      Object.values(chatListeners).forEach((unsubscribe) => {
        unsubscribe();
      });
      setChatListeners({});
    };
  }, [userData?.id, loadChats]);

  // Cleanup listeners when component unmounts
  useEffect(() => {
    return () => {
      Object.values(chatListeners).forEach((unsubscribe) => {
        unsubscribe();
      });
    };
  }, [chatListeners]);

  return {
    chats,
    loading,
    error,
    friendsWithoutChats,
    loadChats,
    startChat,
    sendMessage,
    listenToMessages,
    updateChatLastMessage,
  };
};
