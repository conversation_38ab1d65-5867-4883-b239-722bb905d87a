import React from 'react';
import { View } from 'react-native';

import { useEvent } from '~/providers/MapProvider';
import { scale, verticalScale, moderateScale } from 'react-native-size-matters';

import { Button } from '../ui/button';
import { BlurView } from 'expo-blur';
import { MaterialIcons } from '@expo/vector-icons';
import { MapTsType, MapTsTypeMap, ViewModeMap } from '~/types';

const MapActions: React.FC = () => {
  const {
    setViewMode,
    ViewMode,
    MapType,
    filteredEvents,
    zoomToLocation,
    resetToUserLocation,
    setFollowUserLocation,
    setMapType,
  } = useEvent();

  const Swipe = () => {
    if (ViewMode === ViewModeMap.ListView) {
      setViewMode(ViewModeMap.ScrollView);
      const coodinates = {
        latitude: filteredEvents[0].locationData.coordinates[1],
        longitude: filteredEvents[0].locationData.coordinates[0],
      };

      setTimeout(() => {
        zoomToLocation(coodinates);
      }, 500);
    } else {
      setViewMode(ViewModeMap.ListView);
      resetToUserLocation();
    }
  };

  const switchMapType = (type: MapTsType): void => {
    setMapType(type);
    setFollowUserLocation(false);
    setTimeout(() => {
      resetToUserLocation();
    }, 100);
  };

  return (
    <View
      style={{
        zIndex: 0,
        bottom: ViewMode === ViewModeMap.ScrollView ? verticalScale(70) : verticalScale(15),
        width: '100%',
        position: 'absolute',
        backgroundColor: 'transparent',
        paddingHorizontal: 20,
      }}>
      <View className="flex flex-row items-center justify-between">
        <View>
          {ViewMode === ViewModeMap.ListView && (
            <View className="flex flex-row">
              <Button
                onPress={() => {
                  switchMapType(MapTsTypeMap.Events);
                }}
                className={`${MapType === MapTsTypeMap.Events ? 'bg-light-primary dark:bg-dark-primary' : 'bg-black/40'} rounded-none rounded-l-full `}
                size={'md'}
                variant="solid"
                action="primary">
                <MaterialIcons name="event" size={24} color="white" />
              </Button>

              <Button
                onPress={() => {
                  switchMapType(MapTsTypeMap.People);
                }}
                className={`${MapType === MapTsTypeMap.People ? 'bg-light-primary dark:bg-dark-primary' : `bg-black/40`} rounded-none rounded-r-full `}
                size={'md'}
                variant="solid"
                action="primary">
                <MaterialIcons name="people" size={24} color="white" />
              </Button>
            </View>
          )}
        </View>

        {ViewMode === ViewModeMap.ListView ? (
          <View className="flex flex-row items-center gap-3 space-x-2">
            <Button
              onPress={() => {
                setFollowUserLocation(false);
                setTimeout(() => {
                  resetToUserLocation();
                }, 10);
              }}
              className="p-0 rounded-full bg-none"
              size={'lg'}
              variant="solid"
              action="primary">
              <View className="p-1 overflow-hidden border rounded-full border-white/10 bg-black/40">
                <BlurView className="absolute inset-0" intensity={50} tint="dark" />
                <View className="z-10 p-1">
                  <MaterialIcons name="my-location" size={24} color="white" />
                </View>
              </View>
            </Button>
            <Button
              onPress={Swipe}
              className="p-0 rounded-full bg-none"
              size={'lg'}
              variant="solid"
              action="primary">
              <View className="p-1 overflow-hidden border rounded-full border-white/10 bg-black/40">
                <BlurView className="absolute inset-0" intensity={50} tint="dark" />
                <View className="z-10 p-1">
                  <MaterialIcons name="swipe" size={24} color="white" />
                </View>
              </View>
            </Button>
          </View>
        ) : (
          <Button
            onPress={Swipe}
            className={`bottom-12 rounded-full bg-none p-0`}
            size={'lg'}
            variant="solid"
            action="primary">
            <View className="p-1 overflow-hidden border rounded-full border-white/10 bg-black/50">
              <BlurView className="absolute inset-0" intensity={50} tint="dark" />
              <View className="z-10 p-1">
                <MaterialIcons name="swipe-vertical" size={24} color="white" />
              </View>
            </View>
          </Button>
        )}
      </View>
    </View>
  );
};

export default MapActions;
