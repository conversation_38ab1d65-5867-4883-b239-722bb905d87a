import { Ionicons, MaterialCommunityIcons, MaterialIcons } from '@expo/vector-icons';
import BottomSheet, { BottomSheetScrollView } from '@gorhom/bottom-sheet';
import { format } from 'date-fns';
import { useRouter } from 'expo-router';
import React, { useEffect, useRef, useState } from 'react';
import { Text, View, Image, TouchableOpacity, Linking, Platform } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { Toast } from 'toastify-react-native';

import LocationPreview from '../Map/LocationPreview';
import PersonProfileSheet, { PersonProfileSheetHandle } from '../People/PersonProfileSheet';
import PhotoViewModal from '../Profile/PhotoViewModal';
import { RenderBackdrop } from '../RenderBackdrop';

import { useColorScheme } from '~/lib/useColorScheme';
import { useEvent } from '~/providers/MapProvider';
import { EventService } from '~/services/EventService';
import { UserStore } from '~/store/store';
import { EventType } from '~/types';

// Ticket Info Component
const TicketsContent: React.FC<{
  event: EventType;
  onBack: () => void;
}> = ({ event, onBack }) => {
  const { colors } = useColorScheme();

  const formatDate = (dateString: string | undefined) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return format(date, 'EEEE, MMMM d');
  };

  const formatTime = (dateString: string | undefined) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return format(date, 'h:mm a');
  };

  const getTicketInfo = () => {
    if (!event) return null;

    if (!event.isPaid) {
      return {
        isFree: true,
        price: 'Free',
        totalTickets: event.ticketSetup.totalTickets,
      };
    }

    if (event.ticketSetup.hasLevels) {
      return {
        isFree: false,
        hasLevels: true,
        levels: event.ticketSetup.levels,
      };
    }

    return {
      isFree: false,
      hasLevels: false,
      price: event.ticketSetup.price,
      totalTickets: event.ticketSetup.totalTickets,
    };
  };

  const ticketInfo = getTicketInfo() || { isFree: true, price: 0, totalTickets: 0 };

  return (
    <BottomSheetScrollView contentContainerStyle={{ paddingBottom: 20 }}>
      <View className="p-4">
        {/* Back Button */}
        <TouchableOpacity className="mb-4 flex-row items-center" onPress={onBack}>
          <MaterialIcons name="arrow-back" size={24} color={colors.foreground} />
          <Text className="ml-2 font-medium text-base " style={{ color: colors.foreground }}>
            Back to Event
          </Text>
        </TouchableOpacity>

        <View className="mb-6">
          <Text className="light:text-light-text mb-1 font-bold text-2xl dark:text-dark-text">
            {event?.title}
          </Text>
          <Text className="light:text-light-text/70 font-medium text-base dark:text-dark-text/70">
            {formatDate(event?.startDateTime)} • {formatTime(event?.startDateTime)} to{' '}
            {formatTime(event?.endDateTime)}
          </Text>
        </View>

        <View className="mb-6">
          <Text className="light:text-light-text mb-3 font-bold text-lg dark:text-dark-text">
            Registration
          </Text>
          <Text className="light:text-light-text mb-4 text-base dark:text-dark-text">
            {ticketInfo?.isFree
              ? 'Hello! To join the event, please register below.'
              : 'Select your ticket type to continue with your purchase.'}
          </Text>

          {ticketInfo?.isFree ? (
            <TouchableOpacity className="rounded-lg bg-light-primary py-3 dark:bg-dark-primary">
              <Text className="text-center font-bold text-base text-white">Register</Text>
            </TouchableOpacity>
          ) : ticketInfo?.hasLevels ? (
            <View className="space-y-3">
              {ticketInfo.levels?.map((level: any, index: number) => (
                <View
                  key={index}
                  className="rounded-lg border border-gray-200 p-4 dark:border-gray-700">
                  <View className="mb-2 flex-row items-center justify-between">
                    <Text className="light:text-light-text font-bold text-base dark:text-dark-text">
                      {level.type}
                    </Text>
                    <Text className="light:text-light-primary font-bold text-base dark:text-dark-primary">
                      {event.currency} {level.price}
                    </Text>
                  </View>
                  <Text className="light:text-light-text/70 mb-3 text-sm dark:text-dark-text/70">
                    {level.quantity} tickets available
                  </Text>
                  <TouchableOpacity className="rounded-lg bg-light-primary py-2 dark:bg-dark-primary">
                    <Text className="text-center font-bold text-white">Select</Text>
                  </TouchableOpacity>
                </View>
              ))}
            </View>
          ) : (
            <View className="mb-4 rounded-lg border border-gray-200 p-4 dark:border-gray-700">
              <View className="mb-2 flex-row items-center justify-between">
                <Text className="light:text-light-text font-bold text-base dark:text-dark-text">
                  Standard Ticket
                </Text>
                <Text className="light:text-light-primary font-bold text-base dark:text-dark-primary">
                  {event.currency} {ticketInfo.price}
                </Text>
              </View>
              <Text className="light:text-light-text/70 mb-3 text-sm dark:text-dark-text/70">
                {ticketInfo.totalTickets} tickets available
              </Text>
              <TouchableOpacity className="rounded-lg bg-light-primary py-2 dark:bg-dark-primary">
                <Text className="text-center font-bold text-white">Buy Ticket</Text>
              </TouchableOpacity>
            </View>
          )}
        </View>

        {!ticketInfo?.isFree && (
          <View className="mb-4 flex-row items-start gap-4">
            <MaterialCommunityIcons name="shield" size={24} color={colors.foreground} />
            <View className="flex-1">
              <Text className="light:text-light-text font-medium text-subtitle dark:text-dark-text">
                Buyer Guarantee Protected
              </Text>
              <Text className="font-regular light:text-light-text/70 text-body dark:text-dark-text/70">
                Every ticket is protected. If your event gets canceled, we'll make it right.
              </Text>
            </View>
          </View>
        )}
      </View>
    </BottomSheetScrollView>
  );
};

const EventMarkerSheetContent: React.FC = () => {
  const { selectedEvent, setSelectedEvent } = useEvent();
  const bottomSheetRef = useRef<BottomSheet>(null);
  const personProfileSheetRef = useRef<PersonProfileSheetHandle>(null);
  const { colors } = useColorScheme();
  const [showTickets, setShowTickets] = useState(false);
  const [isSaved, setIsSaved] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [savedEvents, setSavedEvents] = useState<string[]>([]);
  const [photoModalVisible, setPhotoModalVisible] = useState(false);
  const [selectedPhoto, setSelectedPhoto] = useState<any>(null);
  const insets = useSafeAreaInsets();
  const router = useRouter();

  // Get current user from store
  const currentUser = UserStore((state: any) => state.user);

  // Function to open Google Maps
  const openInGoogleMaps = () => {
    if (!selectedEvent?.locationData?.coordinates) {
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Location coordinates not available',
        position: 'bottom',
      });
      return;
    }

    // Coordinates are stored as [longitude, latitude]
    const [longitude, latitude] = selectedEvent.locationData.coordinates;
    const address = selectedEvent.locationData.address || selectedEvent.location || '';

    let url: string;

    if (Platform.OS === 'ios') {
      // For iOS, use Apple Maps or Google Maps app
      url = `maps://app?daddr=${latitude},${longitude}`;
      // Alternative Google Maps URL for iOS: `comgooglemaps://?daddr=${latitude},${longitude}`
    } else {
      // For Android, use Google Maps
      url = `geo:${latitude},${longitude}?q=${latitude},${longitude}(${encodeURIComponent(address)})`;
    }

    Linking.canOpenURL(url)
      .then((supported) => {
        if (supported) {
          return Linking.openURL(url);
        } else {
          // Fallback to web Google Maps
          const webUrl = `https://www.google.com/maps/search/?api=1&query=${latitude},${longitude}`;
          return Linking.openURL(webUrl);
        }
      })
      .catch((err) => {
        console.error('Error opening maps:', err);
        Toast.show({
          type: 'error',
          text1: 'Error',
          text2: 'Could not open maps application',
          position: 'bottom',
        });
      });
  };

  // Fetch saved events for the user
  useEffect(() => {
    const fetchSavedEvents = async () => {
      if (currentUser?.id) {
        try {
          const response = await EventService.getSavedEvents(currentUser.id);
          if (response.success && response.body) {
            // Extract event IDs from the saved events

            const eventIds = response.body.events.map(
              (event: any) => event.id || event.eventId || event._id
            );
            setSavedEvents(eventIds);
          }
        } catch (error) {
          console.error('Error fetching saved events:', error);
        }
      }
    };

    fetchSavedEvents();
  }, [currentUser?.id]);

  // Check if the selected event is saved when selectedEvent or savedEvents change
  useEffect(() => {
    if (selectedEvent && savedEvents.length > 0) {
      const isEventSaved = savedEvents.includes(selectedEvent.id.toString());
      setIsSaved(isEventSaved);
    } else {
      setIsSaved(false);
    }
  }, [selectedEvent, savedEvents]);

  // Handle save/unsave event
  const handleSaveEvent = async () => {
    if (!selectedEvent || !currentUser?.id) {
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Unable to save event. Please try again.',
        position: 'bottom',
      });
      return;
    }

    setIsSaving(true);
    try {
      await EventService.saveEvent(currentUser.id, selectedEvent.id.toString());

      // Update local saved events state
      if (isSaved) {
        // Remove from saved events
        setSavedEvents((prev) => prev.filter((id) => id !== selectedEvent.id.toString()));
      } else {
        // Add to saved events
        setSavedEvents((prev) => [...prev, selectedEvent.id.toString()]);
      }

      setIsSaved(!isSaved);

      Toast.show({
        type: 'success',
        text1: isSaved ? 'Event Unsaved' : 'Event Saved',
        text2: isSaved
          ? 'Event removed from your saved events'
          : 'Event added to your saved events',
        position: 'bottom',
      });
    } catch (error: any) {
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: error.message || 'Failed to save event. Please try again.',
        position: 'bottom',
      });
    } finally {
      setIsSaving(false);
    }
  };

  const handlePhotoPress = (photo: any) => {
    setSelectedPhoto(photo);
    setPhotoModalVisible(true);
  };

  const handleEventImagePress = () => {
    if (selectedEvent) {
      const imageData = {
        id: selectedEvent.id || 'event-image',
        secureUrl: getEventImage(),
        publicId: selectedEvent.coverImage || 'event-cover',
      };
      handlePhotoPress(imageData);
    }
  };

  useEffect(() => {
    if (selectedEvent) {
      console.log(selectedEvent);
      bottomSheetRef.current?.expand();
    } else {
      bottomSheetRef.current?.close();
      setShowTickets(false);
    }
  }, [selectedEvent]);

  const handleSheetChanges = (index: number) => {
    if (index === -1) {
      setSelectedEvent(null);
      setShowTickets(false);
    }
  };

  const getEventImage = () => {
    // Use a default image if no cover image is provided
    if (!selectedEvent?.coverImage) {
      const eventTypeImages: Record<string, string> = {
        Business:
          'https://images.unsplash.com/photo-1565398305935-49e5dcc5c9f6?q=80&w=1920&auto=format',
        Leisure:
          'https://images.unsplash.com/photo-1517457373958-b7bdd4587205?q=80&w=1920&auto=format',
        Entertainment:
          'https://images.unsplash.com/photo-1516450360452-9312f5e86fc7?q=80&w=1920&auto=format',
        Educational:
          'https://images.unsplash.com/photo-1524178232363-1fb2b075b655?q=80&w=1920&auto=format',
      };
      const eventType = selectedEvent?.eventType || '';
      return (
        eventTypeImages[eventType] || 'https://images.unsplash.com/photo-1501281668745-f7f57925c3b4'
      );
    }
    return selectedEvent.coverImage;
  };

  return (
    <BottomSheet
      ref={bottomSheetRef}
      index={selectedEvent ? 0 : -1}
      snapPoints={['100%']}
      enablePanDownToClose
      onClose={() => {
        setSelectedEvent(null);
        setShowTickets(false);
      }}
      onChange={handleSheetChanges}
      backgroundStyle={{ backgroundColor: colors.background }}
      enableOverDrag={false}
      backdropComponent={RenderBackdrop}
      containerStyle={{
        zIndex: 100,
        elevation: 100,
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
      }}
      handleIndicatorStyle={{
        backgroundColor: colors.foreground,
      }}
      topInset={insets.top}
      bottomInset={0}>
      {showTickets && selectedEvent ? (
        <TicketsContent event={selectedEvent} onBack={() => setShowTickets(false)} />
      ) : (
        <BottomSheetScrollView
          showsVerticalScrollIndicator={false}
          contentContainerStyle={{ paddingBottom: 20 }}>
          <View
            className="flex-row items-center justify-between border-b px-4 py-4"
            style={{ borderBottomColor: colors.grey5 }}>
            <Text className="text-lg font-semibold" style={{ color: colors.foreground }}>
              Event Details
            </Text>
            <TouchableOpacity
              className="p-2"
              onPress={() => {
                setSelectedEvent(null);
                setShowTickets(false);
              }}>
              <Ionicons name="close" size={24} color={colors.foreground} />
            </TouchableOpacity>
          </View>

          {/* Event Header Image */}
          <TouchableOpacity
            className="relative h-48 w-full"
            onPress={handleEventImagePress}
            activeOpacity={0.9}>
            <Image
              source={{ uri: getEventImage() }}
              className="h-full w-full"
              style={{ resizeMode: 'cover' }}
            />

            {/* Check if event has expired and show expired tag */}
            {(() => {
              const now = new Date();
              const dateString = selectedEvent?.endDateTime || selectedEvent?.startDateTime;
              if (!dateString) return null;

              const eventEndDate = new Date(dateString);
              const isExpired = eventEndDate < now;

              return isExpired ? (
                <View
                  className="absolute right-2 top-2 rounded-full px-2 py-1"
                  style={{ backgroundColor: '#ef4444' }}>
                  <Text className="font-bold text-xs text-white">Expired</Text>
                </View>
              ) : null;
            })()}

            <View className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-4">
              <Text className="mb-1 font-bold text-2xl uppercase text-white">
                {selectedEvent?.title}
              </Text>
              <Text className="text-base text-white">{selectedEvent?.location}</Text>
            </View>
          </TouchableOpacity>

          {/* Event Details */}
          <View className="p-4">
            <Text className="light:text-light-text mb-4 font-bold text-2xl dark:text-dark-text">
              {selectedEvent?.location} - {selectedEvent?.title}
            </Text>

            {/* Date and Time */}
            <View className="mb-6 flex-row items-center justify-between">
              <View className="flex-1">
                <Text className="light:text-light-text font-medium text-lg dark:text-dark-text">
                  {selectedEvent?.startDateTime
                    ? format(new Date(selectedEvent.startDateTime), 'EEEE')
                    : ''}
                </Text>

                <Text className="light:text-light-text/70 text-base dark:text-dark-text/70">
                  {selectedEvent?.startDateTime
                    ? format(new Date(selectedEvent.startDateTime), 'MMMM d, h:mm a')
                    : ''}{' '}
                  to{' '}
                  {selectedEvent?.endDateTime
                    ? format(new Date(selectedEvent.endDateTime), 'h:mm a')
                    : ''}
                </Text>
                <Text className="light:text-light-text/70 mb-2 text-base dark:text-dark-text/70">
                  {selectedEvent?.locationData.address || selectedEvent?.location}
                </Text>
              </View>
              <View className="items-end" />
            </View>

            {/* Location */}
            <View className="mb-6">
              {selectedEvent?.locationData && (
                <LocationPreview
                  location={{
                    coordinates: selectedEvent.locationData.coordinates,
                    manualAddress: selectedEvent.locationData.address,
                    name: selectedEvent.locationData.name,
                    address: selectedEvent.locationData.address || '',
                  }}
                  editable={false}
                />
              )}

              {/* View Full Map Button */}
              {selectedEvent?.locationData?.coordinates && (
                <TouchableOpacity
                  onPress={openInGoogleMaps}
                  className="mt-3 flex-row items-center justify-center rounded-lg border border-gray-300 px-4 py-2 dark:border-gray-600"
                  style={{ backgroundColor: 'transparent' }}>
                  <MaterialIcons
                    name="map"
                    size={18}
                    color={colors.primary}
                    style={{ marginRight: 8 }}
                  />
                  <Text className="font-medium text-sm" style={{ color: colors.primary }}>
                    View Full Map
                  </Text>
                </TouchableOpacity>
              )}
            </View>

            {/* Registration Section */}
            <View className="mb-6">
              <View className="flex-row gap-3">
                <TouchableOpacity
                  onPress={() => setShowTickets(true)}
                  className="flex-1 flex-row items-center justify-center rounded-full bg-light-primary px-4 py-3 dark:bg-dark-primary">
                  <MaterialIcons
                    name="how-to-reg"
                    size={20}
                    color="white"
                    style={{ marginRight: 8 }}
                  />
                  <Text className="text-center font-bold text-base text-white">
                    {selectedEvent?.isPaid ? 'View Tickets' : 'Register'}
                  </Text>
                </TouchableOpacity>

                <TouchableOpacity
                  onPress={handleSaveEvent}
                  disabled={isSaving}
                  className="flex-row items-center justify-center rounded-full border-2 px-4 py-3"
                  style={{
                    borderColor: colors.primary,
                    backgroundColor: isSaved ? colors.primary : 'transparent',
                    opacity: isSaving ? 0.7 : 1,
                  }}>
                  <Ionicons
                    name={isSaved ? 'bookmark' : 'bookmark-outline'}
                    size={20}
                    color={isSaved ? 'white' : colors.primary}
                  />
                  <Text
                    className="ml-2 font-bold text-sm"
                    style={{ color: isSaved ? 'white' : colors.primary }}>
                    {isSaving ? 'Saving...' : isSaved ? 'Saved' : 'Save'}
                  </Text>
                </TouchableOpacity>
              </View>
            </View>

            {/* About Event Section */}
            <View className="mb-6">
              <Text className="light:text-light-text mb-2 font-bold text-lg dark:text-dark-text">
                About Event
              </Text>
              <Text className="light:text-light-text text-base dark:text-dark-text">
                {selectedEvent?.description}
              </Text>
            </View>

            {/* Event Uploads Section */}
            {(selectedEvent as any)?.eventUploads &&
              (selectedEvent as any).eventUploads.length > 0 && (
                <View className="mb-6">
                  <Text className="light:text-light-text mb-3 font-bold text-lg dark:text-dark-text">
                    Event Photos
                  </Text>
                  <View className="-mx-1 flex-row flex-wrap">
                    {(selectedEvent as any).eventUploads.map((upload: any, index: number) => (
                      <TouchableOpacity
                        key={upload.id || index}
                        className="mb-2 w-1/3 px-1"
                        onPress={() => handlePhotoPress(upload)}>
                        <Image
                          source={{ uri: upload.secureUrl }}
                          className="aspect-square w-full rounded-lg"
                          style={{ resizeMode: 'cover' }}
                        />
                      </TouchableOpacity>
                    ))}
                  </View>
                </View>
              )}

            {/* Hosts Section */}
            <View className="mb-6">
              <Text className="light:text-light-text mb-3 font-bold text-lg dark:text-dark-text">
                Host
              </Text>

              <TouchableOpacity
                className="mb-2 flex-row items-center"
                onPress={() => {
                  // Type assertion to handle extended event properties
                  const eventWithUser = selectedEvent as any;
                  if (eventWithUser?.user?.id && eventWithUser?.user?.email) {
                    // Close the current bottom sheet first
                    setSelectedEvent(null);
                    setShowTickets(false);
                    // Navigate to view profile screen
                    router.push({
                      pathname: '/Auth/viewProfile',
                      params: {
                        userId: eventWithUser.user.id.toString(),
                        email: eventWithUser.user.email.toString(),
                      },
                    });
                  }
                }}>
                <Image
                  source={{
                    uri:
                      (selectedEvent as any)?.user?.profilePicture &&
                      Array.isArray((selectedEvent as any)?.user.profilePicture) &&
                      (selectedEvent as any)?.user.profilePicture.length > 0 &&
                      (selectedEvent as any)?.user.profilePicture[
                        (selectedEvent as any)?.user.profilePicture.length - 1
                      ]?.secureUrl
                        ? (selectedEvent as any)?.user.profilePicture[
                            (selectedEvent as any)?.user.profilePicture.length - 1
                          ].secureUrl
                        : 'https://res.cloudinary.com/dil9qrpak/image/upload/v1748976473/user-circles-set_hrudqa.png',
                  }}
                  className="mr-3 h-10 w-10 rounded-full"
                  style={{ resizeMode: 'cover' }}
                />
                <View className="flex-1">
                  <Text className="light:text-light-text font-medium text-base dark:text-dark-text">
                    {(selectedEvent as any)?.user?.fullName || 'Event Host'}
                  </Text>
                  <View className="mt-1 flex-row items-center">
                    {/* Display 5 stars with random rating for demo purposes */}
                    {[1, 2, 3, 4, 5].map((star) => {
                      // Generate a random rating between 3-5 for demo
                      const rating = 3 + Math.floor(Math.random() * 2.1);
                      return (
                        <Ionicons
                          key={star}
                          name={star <= rating ? 'star' : 'star-outline'}
                          size={14}
                          color="#FFD700"
                          style={{ marginRight: 2 }}
                        />
                      );
                    })}
                    <Text className="light:text-light-text/70 ml-1 text-xs dark:text-dark-text/70">
                      {(3 + Math.random() * 2).toFixed(1)}/5.0
                    </Text>
                  </View>
                </View>
                <Ionicons
                  name="chevron-forward"
                  size={20}
                  color={colors.grey}
                  style={{ marginLeft: 8 }}
                />
              </TouchableOpacity>
            </View>
          </View>
        </BottomSheetScrollView>
      )}
      <PersonProfileSheet ref={personProfileSheetRef} />
      <PhotoViewModal
        visible={photoModalVisible}
        photo={selectedPhoto}
        onClose={() => setPhotoModalVisible(false)}
      />
    </BottomSheet>
  );
};

export default function EventMarkerSheet() {
  return (
    <View
      style={{
        position: 'absolute',
        zIndex: 100,
        width: '100%',
        height: '100%',
        pointerEvents: 'box-none',
      }}>
      <EventMarkerSheetContent />
    </View>
  );
}
