import React, { forwardRef, useImperativeHandle, useRef } from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { useRouter } from 'expo-router';
import { Ionicons, MaterialIcons } from '@expo/vector-icons';
import { useColorScheme } from '~/lib/useColorScheme';
import BottomSheet, { BottomSheetView, BottomSheetBackdrop } from '@gorhom/bottom-sheet';
import { RenderBackdrop } from '~/components/RenderBackdrop';

export interface StoryViewSheetHandle {
  present: (userId: number, userName: string, hasStory: boolean) => void;
  dismiss: () => void;
}

interface StoryViewSheetProps {
  onViewProfile?: (userId: number) => void;
  onViewStory?: (userId: number) => void;
}

const StoryViewSheet = forwardRef<StoryViewSheetHandle, StoryViewSheetProps>(
  ({ onViewProfile, onViewStory }, ref) => {
    const bottomSheetRef = useRef<BottomSheet>(null);
    const { colors, colorScheme } = useColorScheme();
    const isDark = colorScheme === 'dark';
    const router = useRouter();

    const [userId, setUserId] = React.useState<number | null>(null);
    const [userName, setUserName] = React.useState<string>('');
    const [hasStory, setHasStory] = React.useState<boolean>(false);

    const snapPoints = ['25%'];

    useImperativeHandle(ref, () => ({
      present: (userId: number, userName: string, hasStory: boolean) => {
        setUserId(userId);
        setUserName(userName);
        setHasStory(hasStory);
        bottomSheetRef.current?.expand();
      },
      dismiss: () => {
        bottomSheetRef.current?.close();
      },
    }));

    const handleViewProfile = () => {
      bottomSheetRef.current?.close();
      if (userId) {
        if (onViewProfile) {
          onViewProfile(userId);
        } else {
          router.push({
            pathname: '/profile',
            params: { userId },
          });
        }
      }
    };

    const handleViewStory = () => {
      bottomSheetRef.current?.close();
      if (userId && hasStory) {
        if (onViewStory) {
          onViewStory(userId);
        } else {
          router.push({
            pathname: '/story/view',
            params: { userId },
          });
        }
      }
    };

    return (
      <BottomSheet
        ref={bottomSheetRef}
        index={-1}
        snapPoints={snapPoints}
        enablePanDownToClose
        backdropComponent={RenderBackdrop}
        handleIndicatorStyle={{
          backgroundColor: isDark ? '#6b7280' : '#a1a1aa',
          width: 40,
        }}
        backgroundStyle={{
          backgroundColor: colors.background,
        }}>
        <BottomSheetView className="flex-1 px-4 pt-2">
          <Text
            className={`text-center font-medium text-lg mb-6 ${isDark ? 'text-white' : 'text-black'}`}>
            {userName}
          </Text>

          <TouchableOpacity
            className="flex-row items-center py-4"
            onPress={handleViewProfile}>
            <View
              className="w-10 h-10 items-center justify-center rounded-full mr-4"
              style={{ backgroundColor: colors.grey5 }}>
              <Ionicons name="person" size={22} color={colors.foreground} />
            </View>
            <Text
              className={`font-medium text-base ${isDark ? 'text-white' : 'text-black'}`}>
              View Profile
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            className={`flex-row items-center py-4 ${!hasStory ? 'opacity-50' : ''}`}
            onPress={handleViewStory}
            disabled={!hasStory}>
            <View
              className="w-10 h-10 items-center justify-center rounded-full mr-4"
              style={{ backgroundColor: colors.grey5 }}>
              <Ionicons name="play-circle" size={22} color={colors.foreground} />
            </View>
            <Text
              className={`font-medium text-base ${isDark ? 'text-white' : 'text-black'}`}>
              {hasStory ? 'View Story' : 'No Story Available'}
            </Text>
          </TouchableOpacity>
        </BottomSheetView>
      </BottomSheet>
    );
  }
);

export default StoryViewSheet;
