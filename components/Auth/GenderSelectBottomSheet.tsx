import React, { useRef, forwardRef, useImperativeHandle } from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { useColorScheme } from '~/lib/useColorScheme';
import { Ionicons } from '@expo/vector-icons';
import BottomSheet, {
  BottomSheetView,
  BottomSheetBackdrop,
  BottomSheetModal,
} from '@gorhom/bottom-sheet';
import { RenderBackdrop } from '../RenderBackdrop';

export interface GenderSelectBottomSheetProps {
  currentValue: string;
  onSave: (value: string) => void;
}

export interface GenderSelectBottomSheetHandle {
  present: () => void;
  dismiss: () => void;
}

const GenderSelectBottomSheet = forwardRef<
  GenderSelectBottomSheetHandle,
  GenderSelectBottomSheetProps
>(({ currentValue, onSave }, ref) => {
  const bottomSheetModalRef = useRef<BottomSheetModal>(null);
  const [selected, setSelected] = React.useState('');
  const { colorScheme, colors } = useColorScheme();
  const isDark = colorScheme === 'dark';

  // Gender options
  const genderOptions = ['male', 'female'];

  useImperativeHandle(ref, () => ({
    present: () => {
      setSelected(currentValue);
      bottomSheetModalRef.current?.expand();
    },
    dismiss: () => bottomSheetModalRef.current?.close(),
  }));

  const handleSave = () => {
    onSave(selected);
    bottomSheetModalRef.current?.close();
  };

  return (
    <BottomSheet
      ref={bottomSheetModalRef}
      index={-1}
      snapPoints={['40%']}
      backdropComponent={RenderBackdrop}
      backgroundStyle={{
        backgroundColor: colors.background,
        borderTopLeftRadius: 24,
        borderTopRightRadius: 24,
      }}
      handleIndicatorStyle={{
        backgroundColor: isDark ? '#555' : '#999',
        width: 40,
      }}>
      <BottomSheetView className="flex-1 px-5 pt-2">
        <View className="mb-5 flex-row items-center justify-between">
          <Text className={`text-xl font-semibold ${isDark ? 'text-white' : 'text-black'}`}>
            Select Gender
          </Text>
          <TouchableOpacity className="p-2" onPress={() => bottomSheetModalRef.current?.close()}>
            <Ionicons name="close" size={24} color={isDark ? '#fff' : '#000'} />
          </TouchableOpacity>
        </View>

        <View className="mb-4">
          {genderOptions.map((gender) => (
            <TouchableOpacity
              key={gender}
              onPress={() => setSelected(gender)}
              className={`flex-row items-center border-b py-3.5 `}
              style={{ borderBottomColor: colors.grey5 }}>
              <View
                className={`mr-3 h-5 w-5 items-center justify-center rounded-full border-2 ${
                  selected === gender
                    ? 'border-violet-600'
                    : isDark
                      ? 'border-gray-500'
                      : 'border-gray-400'
                }`}>
                {selected === gender && <View className="h-2.5 w-2.5 rounded-full bg-violet-600" />}
              </View>
              <Text className={`${isDark ? 'text-white' : 'text-black'} capitalize`}>{gender}</Text>
            </TouchableOpacity>
          ))}
        </View>

        <TouchableOpacity
          onPress={handleSave}
          className="mt-2 items-center rounded-lg     style={{ backgroundColor: colors.background }} bg-violet-600 py-3.5">
          <Text className="text-base font-semibold text-white">Save</Text>
        </TouchableOpacity>
      </BottomSheetView>
    </BottomSheet>
  );
});

export default GenderSelectBottomSheet;
