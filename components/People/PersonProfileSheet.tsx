import React, { forwardRef, useRef, useState, useEffect } from 'react';
import { View, Text, TouchableOpacity, Image, ScrollView } from 'react-native';
import { useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import BottomSheet, { BottomSheetScrollView } from '@gorhom/bottom-sheet';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

import PhotoViewModal from '~/components/Profile/PhotoViewModal';
import { RenderBackdrop } from '~/components/RenderBackdrop';
import { useColorScheme } from '~/lib/useColorScheme';
import { useEvent } from '~/providers/MapProvider';
import { FileService } from '~/services/FileService';
import { FriendService } from '~/services/FriendService';
import { Person } from '~/types/people_type';
import { UserStore } from '~/store/store';
import { Toast } from 'toastify-react-native';

export interface PersonProfileSheetHandle {
  present: (userId: string | number) => void;
  dismiss: () => void;
}

interface PersonProfileSheetProps {
  onViewStory?: (userId: string | number) => void;
  onChat?: (userId: string | number) => void;
  onAddFriend?: (userId: string | number) => void;
}

const PersonProfileSheet = forwardRef<PersonProfileSheetHandle, PersonProfileSheetProps>(
  ({ onViewStory, onChat, onAddFriend }, ref) => {
    const bottomSheetRef = useRef<BottomSheet>(null);
    const { colors, colorScheme } = useColorScheme();
    const isDark = colorScheme === 'dark';
    const router = useRouter();
    const { userId, setUserId, setIsPersonProfileSheetOpen, People } = useEvent();
    const user = UserStore((state: any) => state.user);
    const [person, setPerson] = useState<Person | null>(null);
    const [isFriend, setIsFriend] = useState(false);
    const [hasSentFriendRequest, setHasSentFriendRequest] = useState(false);
    const [hasReceivedFriendRequest, setHasReceivedFriendRequest] = useState(false);
    const [receivedFriendRequestId, setReceivedFriendRequestId] = useState<string | null>(null);
    const [hasStory, setHasStory] = useState(false);
    const [selectedPhoto, setSelectedPhoto] = useState<any>(null);
    const [isPhotoModalVisible, setIsPhotoModalVisible] = useState(false);
    const [isAddingFriend, setIsAddingFriend] = useState(false);
    const [isAcceptingRequest, setIsAcceptingRequest] = useState(false);
    const insets = useSafeAreaInsets();

    useEffect(() => {
      if (userId) {
        bottomSheetRef.current?.expand();
      } else {
        bottomSheetRef.current?.close();
      }
    }, [userId]);

    useEffect(() => {
      if (userId) {
        const foundPerson = People.find((p) => p.id.toString() === userId.toString());
        if (foundPerson) {
          setPerson(foundPerson);
          // Check friendship status
          checkFriendshipStatus(userId.toString());
          // Check if user has stories
          checkUserStories(userId.toString());
          // Check sent friend requests
          checkSentFriendRequests(userId.toString());
          // Check received friend requests
          checkReceivedFriendRequests(userId.toString());
        }
      }
    }, [userId, People]);

    const checkFriendshipStatus = async (userIdToCheck: string) => {
      if (!user?.id) return;

      try {
        const response = await FriendService.getFriends(user.id);

        if (response.success && response.body && response.body.friends) {
          const isFriendAlready = response.body.friends.some(
            (friend: any) => friend.id.toString() === userIdToCheck
          );
          setIsFriend(isFriendAlready);
        } else {
          setIsFriend(false);
        }
      } catch (error) {
        console.error('Error checking friendship status:', error);
        setIsFriend(false);
      }
    };

    const checkUserStories = async (userIdToCheck: string) => {
      try {
        const response = await FileService.getUserStories(userIdToCheck);

        if (
          response.success &&
          response.body &&
          Array.isArray(response.body) &&
          response.body.length > 0
        ) {
          // Check if user has active stories
          const activeStories = response.body.some(
            (story: any) =>
              story.storyImages && Array.isArray(story.storyImages) && story.storyImages.length > 0
          );
          setHasStory(activeStories);
        } else {
          setHasStory(false);
        }
      } catch (error) {
        console.error('Error checking user stories:', error);
        setHasStory(false);
      }
    };

    const checkSentFriendRequests = async (userIdToCheck: string) => {
      if (!user?.id) return;

      try {
        const response = await FriendService.getSentFriendRequests(user.id);

        if (response.success && response.body && Array.isArray(response.body)) {
          const hasSentRequest = response.body.some(
            (request: any) =>
              request.requestee?.id?.toString() === userIdToCheck && request.status === 'PENDING'
          );
          setHasSentFriendRequest(hasSentRequest);
        } else {
          setHasSentFriendRequest(false);
        }
      } catch (error) {
        console.error('Error checking sent friend requests:', error);
        setHasSentFriendRequest(false);
      }
    };

    const checkReceivedFriendRequests = async (userIdToCheck: string) => {
      if (!user?.id) return;

      try {
        const response = await FriendService.getFriendRequests(user.id);

        if (response.success && response.body && Array.isArray(response.body)) {
          const receivedRequest = response.body.find(
            (request: any) =>
              request.requester?.id?.toString() === userIdToCheck && request.status === 'PENDING'
          );

          if (receivedRequest) {
            setHasReceivedFriendRequest(true);
            setReceivedFriendRequestId(receivedRequest.id);
          } else {
            setHasReceivedFriendRequest(false);
            setReceivedFriendRequestId(null);
          }
        } else {
          setHasReceivedFriendRequest(false);
          setReceivedFriendRequestId(null);
        }
      } catch (error) {
        console.error('Error checking received friend requests:', error);
        setHasReceivedFriendRequest(false);
        setReceivedFriendRequestId(null);
      }
    };

    const handleViewStory = () => {
      setIsPersonProfileSheetOpen(false);
      bottomSheetRef.current?.close();
      if (userId && hasStory) {
        if (onViewStory) {
          onViewStory(userId);
        } else {
          router.push({
            pathname: '/story/view_other',
            params: { userId: userId.toString() },
          });
        }
      }
    };

    const handleChat = () => {
      setIsPersonProfileSheetOpen(false);
      bottomSheetRef.current?.close();
      if (userId) {
        if (onChat) {
          onChat(userId);
        } else {
          router.push({
            pathname: '/chat',
            params: { userId: userId.toString(), name: person?.name },
          });
        }
      }
    };

    const handleAddFriend = async () => {
      if (!userId || !user?.id) return;

      if (onAddFriend) {
        onAddFriend(userId);
        return;
      }

      try {
        setIsAddingFriend(true);
        const response = await FriendService.sendFriendRequest(user.id, userId.toString());

        if (response.success) {
          setHasSentFriendRequest(true);
          Toast.show({
            type: 'success',
            text1: 'Friend Request Sent',
            text2: `Friend request sent to ${person?.name}`,
            position: 'bottom',
            theme: isDark ? 'dark' : 'light',
            backgroundColor: colors.background,
            autoHide: true,
          });
          // Note: Don't set isFriend to true immediately since it's just a request
          // The friendship status will be updated when the request is accepted
        }
      } catch (error) {
        console.error('Error sending friend request:', error);
        Toast.show({
          type: 'error',
          text1: 'Error',
          text2: 'Failed to send friend request. Please try again.',
          position: 'bottom',
          theme: isDark ? 'dark' : 'light',
          backgroundColor: colors.background,
          autoHide: true,
        });
      } finally {
        setIsAddingFriend(false);
      }
    };

    const handleAcceptFriendRequest = async () => {
      if (!receivedFriendRequestId || !user?.id) return;

      try {
        setIsAcceptingRequest(true);
        const response = await FriendService.acceptFriendRequest(
          receivedFriendRequestId,
          'ACCEPTED'
        );

        if (response.success) {
          setIsFriend(true);
          setHasReceivedFriendRequest(false);
          setReceivedFriendRequestId(null);
          Toast.show({
            type: 'success',
            text1: 'Friend Request Accepted',
            text2: `You are now friends with ${person?.name}`,
            position: 'bottom',
            theme: isDark ? 'dark' : 'light',
            backgroundColor: colors.background,
            autoHide: true,
          });
        }
      } catch (error) {
        console.error('Error accepting friend request:', error);
        Toast.show({
          type: 'error',
          text1: 'Error',
          text2: 'Failed to accept friend request. Please try again.',
          position: 'bottom',
          theme: isDark ? 'dark' : 'light',
          backgroundColor: colors.background,
          autoHide: true,
        });
      } finally {
        setIsAcceptingRequest(false);
      }
    };

    const formatTimeAgo = (dateString: string) => {
      if (!dateString) return 'Offline';

      const date = new Date(dateString);
      const now = new Date();
      const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

      if (diffInSeconds < 60) {
        return `${diffInSeconds}s ago`;
      } else if (diffInSeconds < 3600) {
        return `${Math.floor(diffInSeconds / 60)}m ago`;
      } else if (diffInSeconds < 86400) {
        return `${Math.floor(diffInSeconds / 3600)}h ago`;
      } else {
        return `${Math.floor(diffInSeconds / 86400)}d ago`;
      }
    };

    const handlePhotoPress = (photo: any) => {
      setSelectedPhoto(photo);
      setIsPhotoModalVisible(true);
    };

    const handleProfileImagePress = () => {
      if (person?.profilePhoto) {
        const imageData = {
          id: person.id || 'profile-image',
          secureUrl: person.profilePhoto,
          publicId: person.id || 'profile-photo',
        };
        handlePhotoPress(imageData);
      }
    };

    const handleClosePhotoModal = () => {
      setIsPhotoModalVisible(false);
      setSelectedPhoto(null);
    };

    if (!person) return null;

    return (
      <BottomSheet
        ref={bottomSheetRef}
        index={userId ? 0 : -1}
        snapPoints={['100%']}
        enablePanDownToClose
        backdropComponent={RenderBackdrop}
        handleIndicatorStyle={{
          backgroundColor: isDark ? '#6b7280' : '#a1a1aa',
          width: 40,
        }}
        containerStyle={{
          zIndex: 100,
          elevation: 100,
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
        }}
        backgroundStyle={{
          backgroundColor: colors.background,
        }}
        topInset={insets.top}
        onClose={() => {
          setUserId(null);
          setIsPersonProfileSheetOpen(false);
        }}>
        <BottomSheetScrollView
          contentContainerStyle={{ paddingBottom: 30 }}
          showsVerticalScrollIndicator={false}>
          {/* Profile Header */}
          <View
            className="flex-row items-center justify-between border-b px-4 py-4"
            style={{ borderBottomColor: colors.grey5 }}>
            <Text className="text-lg font-semibold" style={{ color: colors.foreground }}>
              {person.name}'s Profile
            </Text>
            <TouchableOpacity
              className="p-2"
              onPress={() => {
                setUserId(null);
                setIsPersonProfileSheetOpen(false);
              }}>
              <Ionicons name="close" size={24} color={colors.foreground} />
            </TouchableOpacity>
          </View>
          <View className="relative">
            {/* Cover Image */}

            <View className="h-32 bg-violet-600/20" />

            {/* Profile Image */}
            <View className="absolute left-4 top-16 items-center">
              <View
                className={`rounded-full p-1 ${hasStory ? 'bg-transparent' : 'bg-transparent'}`}>
                <TouchableOpacity onPress={handleProfileImagePress} activeOpacity={0.8}>
                  <Image
                    source={{ uri: person.profilePhoto }}
                    className="h-24 w-24 rounded-full border-2 border-white"
                  />
                </TouchableOpacity>
              </View>
            </View>

            {/* Action Buttons */}
            <View className="mt-12 flex-row justify-end p-4">
              {hasStory && (
                <TouchableOpacity
                  className="mr-3 h-10 w-10 items-center justify-center rounded-full bg-violet-600"
                  onPress={handleViewStory}>
                  <Ionicons name="play" size={18} color="#fff" />
                </TouchableOpacity>
              )}

              <TouchableOpacity
                className="mr-3 h-10 w-10 items-center justify-center rounded-full"
                style={{ backgroundColor: colors.grey5 }}
                onPress={handleChat}>
                <Ionicons name="chatbubble-outline" size={18} color={colors.foreground} />
              </TouchableOpacity>

              {!isFriend ? (
                hasReceivedFriendRequest ? (
                  <TouchableOpacity
                    className="h-10 flex-row items-center justify-center rounded-full bg-green-600 px-4"
                    onPress={handleAcceptFriendRequest}
                    disabled={isAcceptingRequest}>
                    <Ionicons name="person-add" size={16} color="#fff" className="mr-1" />
                    <Text className="ml-1 font-medium text-white">
                      {isAcceptingRequest ? 'Accepting...' : 'Accept Request'}
                    </Text>
                  </TouchableOpacity>
                ) : hasSentFriendRequest ? (
                  <TouchableOpacity
                    className="h-10 flex-row items-center justify-center rounded-full px-4"
                    style={{ backgroundColor: colors.grey5 }}
                    disabled>
                    <Ionicons
                      name="checkmark-circle"
                      size={16}
                      color={colors.foreground}
                      className="mr-1"
                    />
                    <Text style={{ color: colors.foreground }} className="ml-1 font-medium">
                      Request Sent
                    </Text>
                  </TouchableOpacity>
                ) : (
                  <TouchableOpacity
                    className="h-10 flex-row items-center justify-center rounded-full bg-violet-600 px-4"
                    onPress={handleAddFriend}
                    disabled={isAddingFriend}>
                    <Ionicons name="person-add-outline" size={16} color="#fff" className="mr-1" />
                    <Text className="ml-1 font-medium text-white">
                      {isAddingFriend ? 'Sending...' : 'Add Friend'}
                    </Text>
                  </TouchableOpacity>
                )
              ) : (
                <TouchableOpacity
                  className="h-10 flex-row items-center justify-center rounded-full px-4"
                  style={{ backgroundColor: colors.grey5 }}>
                  <Ionicons name="checkmark" size={16} color={colors.foreground} className="mr-1" />
                  <Text style={{ color: colors.foreground }} className="ml-1 font-medium">
                    Friends
                  </Text>
                </TouchableOpacity>
              )}
            </View>

            {/* View Full Profile Button */}
            <View className="mb-4 px-4">
              <TouchableOpacity
                className="w-full rounded-lg px-4 py-3"
                style={{ backgroundColor: colors.grey5 }}
                onPress={() => {
                  setUserId(null);
                  setIsPersonProfileSheetOpen(false);
                  router.push({
                    pathname: '/Auth/viewProfile',
                    params: { userId: userId?.toString() || '', email: person?.email.toString() },
                  });
                }}>
                <Text className="text-center font-medium" style={{ color: colors.foreground }}>
                  View Full Profile
                </Text>
              </TouchableOpacity>
            </View>
          </View>

          {/* Profile Info */}
          <View className="mt-2 px-4">
            <Text className={`font-bold text-2xl ${isDark ? 'text-white' : 'text-black'}`}>
              {person.name}
            </Text>

            <View className="mb-3 mt-1 flex-row items-center">
              <View
                className={`mr-2 h-2.5 w-2.5 rounded-full ${person.isOnline ? 'bg-green-500' : 'bg-gray-400'}`}
              />
              <Text className={`${isDark ? 'text-gray-300' : 'text-gray-600'}`}>
                {person.isOnline ? 'Online' : formatTimeAgo(person.lastActive)}
              </Text>
            </View>

            {person.bio && (
              <Text className={`mb-4 text-base ${isDark ? 'text-white' : 'text-black'}`}>
                {person.bio}
              </Text>
            )}

            {/* Profile Photos */}
            {person.profileUploads && person.profileUploads.length > 0 && (
              <View className="mb-6">
                <Text className={`mb-2 font-bold text-lg ${isDark ? 'text-white' : 'text-black'}`}>
                  Photos
                </Text>
                <ScrollView horizontal showsHorizontalScrollIndicator={false}>
                  {person.profileUploads.map((photo, index) => (
                    <TouchableOpacity
                      key={photo.id}
                      className="mr-2 overflow-hidden rounded-lg"
                      onPress={() => handlePhotoPress(photo)}>
                      <Image
                        source={{ uri: photo.secureUrl }}
                        className="h-20 w-20 rounded-lg"
                        resizeMode="cover"
                      />
                    </TouchableOpacity>
                  ))}
                </ScrollView>
              </View>
            )}

            {/* Interests */}
            {person.interests && person.interests.length > 0 && (
              <View className="mb-6">
                <Text className={`mb-2 font-bold text-lg ${isDark ? 'text-white' : 'text-black'}`}>
                  Interests
                </Text>
                <View className="flex-row flex-wrap">
                  {person.interests.map((interest, index) => (
                    <View
                      key={index}
                      className="mb-2 mr-2 rounded-full px-3 py-1.5"
                      style={{ backgroundColor: colors.grey5 }}>
                      <Text className="font-medium text-sm" style={{ color: colors.foreground }}>
                        {interest}
                      </Text>
                    </View>
                  ))}
                </View>
              </View>
            )}

            {/* Up For */}
            {person.upFor && (
              <View className="mb-6">
                <Text className={`mb-2 font-bold text-lg ${isDark ? 'text-white' : 'text-black'}`}>
                  Up For
                </Text>
                <View className="rounded-lg px-3 py-2" style={{ backgroundColor: colors.grey5 }}>
                  <Text className="text-base" style={{ color: colors.foreground }}>
                    {person.upFor}
                  </Text>
                </View>
              </View>
            )}
          </View>
        </BottomSheetScrollView>

        {/* Photo View Modal */}
        <PhotoViewModal
          visible={isPhotoModalVisible}
          photo={selectedPhoto}
          onClose={handleClosePhotoModal}
        />
      </BottomSheet>
    );
  }
);

export default PersonProfileSheet;
