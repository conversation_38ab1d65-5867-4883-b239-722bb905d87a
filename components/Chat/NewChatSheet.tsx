import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  Image,
  TextInput,
  ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import BottomSheet, { BottomSheetView } from '@gorhom/bottom-sheet';
import { FriendService } from '~/services/FriendService';
import { UserStore } from '~/store/store';
import { Toast } from 'toastify-react-native';

interface Friend {
  id: string;
  name: string;
  avatar: string;
  status: string;
}

interface NewChatSheetProps {
  bottomSheetRef: React.RefObject<BottomSheet>;
  onSelectFriend: (friend: Friend) => void;
  colors: any;
  isDark: boolean;
  friendsWithoutChats?: Friend[]; // Add this prop
}

const NewChatSheet = ({
  bottomSheetRef,
  onSelectFriend,
  colors,
  isDark,
  friendsWithoutChats = [],
}: NewChatSheetProps) => {
  const user = UserStore((state: any) => state.user);
  const [searchQuery, setSearchQuery] = useState('');
  const [friends, setFriends] = useState<Friend[]>([]);
  const [filteredFriends, setFilteredFriends] = useState<Friend[]>([]);
  const [loading, setLoading] = useState(true);

  // Fetch friends on component mount
  useEffect(() => {
    fetchFriends();
  }, [user?.id]);

  const fetchFriends = async () => {
    if (!user?.id) return;

    try {
      setLoading(true);
      const response = await FriendService.getFriends(user.id);
      if (response.success && response.body) {
        // Transform the response to match our Friend interface
        const friendsData = response.body.friends.map((friend: any) => ({
          id: friend.id,
          name: friend.fullName || friend.name,
          avatar:
            friend.profilePicture?.[0]?.secureUrl ||
            friend.profilePhoto ||
            'https://res.cloudinary.com/dil9qrpak/image/upload/v1748976473/user-circles-set_hrudqa.png',
          status: friend.isOnline ? 'online' : 'offline',
        }));
        setFriends(friendsData);
        setFilteredFriends(friendsData);
      } else {
        setFriends([]);
        setFilteredFriends([]);
      }
    } catch (error) {
      console.error('Error fetching friends:', error);
      setFriends([]);
      setFilteredFriends([]);
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Failed to load friends',
        position: 'bottom',
        theme: isDark ? 'dark' : 'light',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = (text: string) => {
    setSearchQuery(text);
    if (text.trim() === '') {
      setFilteredFriends(friends);
    } else {
      const filtered = friends.filter((friend) =>
        friend.name.toLowerCase().includes(text.toLowerCase())
      );
      setFilteredFriends(filtered);
    }
  };

  const renderFriend = ({ item }: { item: Friend }) => (
    <TouchableOpacity
      className="flex-row items-center px-4 py-3 border-b"
      style={{ borderBottomColor: colors.grey5 }}
      onPress={() => onSelectFriend(item)}>
      <View className="relative">
        <Image source={{ uri: item.avatar }} className="w-12 h-12 rounded-full" />
        <View
          className="absolute bottom-0 right-0 w-3 h-3 border-2 rounded-full"
          style={{
            borderColor: isDark ? colors.background : colors.root,
          }}
        />
      </View>
      <View className="ml-3">
        <Text className="text-base font-medium" style={{ color: colors.foreground }}>
          {item.name}
        </Text>
      </View>
    </TouchableOpacity>
  );

  return (
    <BottomSheetView style={{ backgroundColor: colors.background, flex: 1 }}>
      <View className="px-4 pt-2 pb-6">
        <Text className="mb-4 text-xl font-bold text-center" style={{ color: colors.foreground }}>
          New Chat
        </Text>

        <View className="mb-4">
          <View
            className="flex-row items-center rounded-xl px-3 py-2.5"
            style={{ backgroundColor: colors.grey5 }}>
            <Ionicons name="search" size={20} color={colors.grey} />
            <TextInput
              className="flex-1 ml-2 text-base"
              placeholder="Search friends..."
              placeholderTextColor={colors.grey}
              value={searchQuery}
              onChangeText={handleSearch}
              style={{ color: colors.foreground }}
            />
            {searchQuery ? (
              <TouchableOpacity onPress={() => handleSearch('')}>
                <Ionicons name="close-circle" size={20} color={colors.grey} />
              </TouchableOpacity>
            ) : null}
          </View>
        </View>

        <Text className="mb-2 text-base font-semibold" style={{ color: colors.grey }}>
          Friends
        </Text>

        {loading ? (
          <View className="items-center justify-center flex-1 py-10">
            <ActivityIndicator size="large" color={colors.primary} />
            <Text className="mt-4 text-base text-center" style={{ color: colors.grey }}>
              Loading friends...
            </Text>
          </View>
        ) : friends.length === 0 ? (
          <View className="items-center justify-center flex-1 py-10">
            <Ionicons name="people" size={48} color={colors.grey} />
            <Text
              className="mt-4 text-base font-medium text-center"
              style={{ color: colors.foreground }}>
              No Friends
            </Text>
            <Text className="mt-2 text-sm text-center" style={{ color: colors.grey }}>
              Add some friends to start chatting!
            </Text>
          </View>
        ) : filteredFriends.length > 0 ? (
          <FlatList
            data={filteredFriends}
            keyExtractor={(item) => item.id}
            renderItem={renderFriend}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={{ paddingBottom: 40 }}
          />
        ) : (
          <View className="items-center justify-center flex-1 py-10">
            <Ionicons name="search" size={48} color={colors.grey} />
            <Text className="mt-4 text-base text-center" style={{ color: colors.grey }}>
              No friends found with "{searchQuery}"
            </Text>
          </View>
        )}
      </View>
    </BottomSheetView>
  );
};

export default NewChatSheet;
